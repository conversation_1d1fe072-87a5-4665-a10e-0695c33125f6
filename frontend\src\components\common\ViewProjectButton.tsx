import React from 'react';
import { Eye } from 'lucide-react';

interface ViewProjectButtonProps {
  onClick: () => void;
}

const ViewProjectButton: React.FC<ViewProjectButtonProps> = ({ onClick }) => {
  return (
    <button
      type="button"
      className="flex w-full items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
      onClick={onClick}
    >
      <Eye className="mr-3 h-4 w-4" />
      View Project
    </button>
  );
};

export default ViewProjectButton;
