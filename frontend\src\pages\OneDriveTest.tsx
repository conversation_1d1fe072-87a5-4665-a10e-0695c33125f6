import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, Cloud, Loader2, Info } from 'lucide-react';
import OneDriveButton from '@/components/ui/OneDriveButton';
import OneDriveDebug from '@/components/OneDriveDebug';
import OneDriveTestButton from '@/components/OneDriveTestButton';
import OneDriveDiagnostic from '@/components/OneDriveDiagnostic';
import oneDriveService from '@/services/oneDriveService';
import { toast } from 'sonner';

const OneDriveTest: React.FC = () => {
  const [authStatus, setAuthStatus] = useState<'checking' | 'authenticated' | 'not-authenticated'>('checking');
  const [testResults, setTestResults] = useState<{
    authUrl: boolean | null;
    authentication: boolean | null;
    pdfUpload: boolean | null;
  }>({
    authUrl: null,
    authentication: null,
    pdfUpload: null
  });

  useEffect(() => {
    checkAuthStatus();

    // Check if this is a callback from OneDrive authentication
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const error = urlParams.get('error');

    if (code || error) {
      handleAuthCallback();
    }
  }, []);

  const handleAuthCallback = async () => {
    try {
      const result = await oneDriveService.handleAuthCallback();

      if (result.success) {
        toast.success('OneDrive authentication successful!', {
          description: 'You can now save invoices to OneDrive.'
        });
        setAuthStatus('authenticated');
      } else {
        toast.error('OneDrive authentication failed', {
          description: result.error
        });
      }
    } catch (error) {
      console.error('Error handling auth callback:', error);
      toast.error('Error handling authentication callback');
    }
  };

  const checkAuthStatus = async () => {
    try {
      const status = await oneDriveService.checkAuthentication();
      setAuthStatus(status.authenticated ? 'authenticated' : 'not-authenticated');
    } catch (error) {
      console.error('Error checking auth status:', error);
      setAuthStatus('not-authenticated');
    }
  };

  const testAuthUrl = async () => {
    try {
      const response = await oneDriveService.getAuthorizationUrl();
      setTestResults(prev => ({ ...prev, authUrl: response.success }));
      
      if (response.success) {
        toast.success('Authorization URL generated successfully');
      } else {
        toast.error('Failed to generate authorization URL');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, authUrl: false }));
      toast.error('Error testing authorization URL');
    }
  };

  const testAuthentication = async () => {
    try {
      const result = await oneDriveService.authenticate();
      setTestResults(prev => ({ ...prev, authentication: result.success }));
      
      if (result.success) {
        setAuthStatus('authenticated');
        toast.success('Authentication successful');
      } else {
        toast.error(result.error || 'Authentication failed');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, authentication: false }));
      toast.error('Authentication test failed');
    }
  };

  const testPdfUpload = async () => {
    try {
      // Create a simple test PDF blob
      const testPdfContent = 'JVBERi0xLjQKJdPr6eEKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIgMCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBvYmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQovUmVzb3VyY2VzIDw8Ci9Gb250IDw8Ci9GMSA0IDAgUgo+Pgo+PgovQ29udGVudHMgNSAwIFIKPj4KZW5kb2JqCjQgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUgL1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iago1IDAgb2JqCjw8Ci9MZW5ndGggNDQKPj4Kc3RyZWFtCkJUCi9GMSAxMiBUZgoxMDAgNzAwIFRkCihIZWxsbyBXb3JsZCkgVGoKRVQKZW5kc3RyZWFtCmVuZG9iago2IDAgb2JqCjw8Ci9UeXBlIC9YUmVmCi9TaXplIDcKL1Jvb3QgMSAwIFIKPj4Kc3RhcnR4cmVmCjU2NwolJUVPRgo=';
      const pdfBlob = new Blob([atob(testPdfContent)], { type: 'application/pdf' });
      
      const response = await oneDriveService.uploadPdf(pdfBlob, 'test-invoice');
      setTestResults(prev => ({ ...prev, pdfUpload: response.success }));
      
      if (response.success) {
        toast.success('PDF upload test successful');
      } else {
        toast.error('PDF upload test failed');
      }
    } catch (error) {
      setTestResults(prev => ({ ...prev, pdfUpload: false }));
      toast.error('PDF upload test failed');
    }
  };

  const getStatusIcon = (status: boolean | null) => {
    if (status === null) return <AlertCircle className="h-4 w-4 text-gray-400" />;
    if (status === true) return <CheckCircle className="h-4 w-4 text-green-600" />;
    return <XCircle className="h-4 w-4 text-red-600" />;
  };

  const getStatusBadge = (status: boolean | null) => {
    if (status === null) return <Badge variant="secondary">Not Tested</Badge>;
    if (status === true) return <Badge variant="default" className="bg-green-600">Pass</Badge>;
    return <Badge variant="destructive">Fail</Badge>;
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          OneDrive Integration Test
        </h1>
        <p className="text-gray-600">
          Test the OneDrive integration functionality and authentication.
        </p>
      </div>

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cloud className="h-5 w-5" />
            Authentication Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            {authStatus === 'checking' ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Checking authentication...</span>
              </>
            ) : authStatus === 'authenticated' ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Authenticated with OneDrive</span>
                <Badge variant="default" className="bg-green-600">Connected</Badge>
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4 text-red-600" />
                <span>Not authenticated with OneDrive</span>
                <Badge variant="destructive">Disconnected</Badge>
              </>
            )}
          </div>
          
          {authStatus === 'not-authenticated' && (
            <div className="mt-4">
              <Button onClick={testAuthentication} variant="outline">
                <Cloud className="mr-2 h-4 w-4" />
                Save to OneDrive
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Integration Tests</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Authorization URL Test */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(testResults.authUrl)}
              <span>Authorization URL Generation</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(testResults.authUrl)}
              <Button onClick={testAuthUrl} size="sm" variant="outline">
                Test
              </Button>
            </div>
          </div>

          {/* Authentication Test */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(testResults.authentication)}
              <span>Authentication Flow</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(testResults.authentication)}
              <Button onClick={testAuthentication} size="sm" variant="outline">
                Test
              </Button>
            </div>
          </div>

          {/* PDF Upload Test */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-2">
              {getStatusIcon(testResults.pdfUpload)}
              <span>PDF Upload</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(testResults.pdfUpload)}
              <Button 
                onClick={testPdfUpload} 
                size="sm" 
                variant="outline"
                disabled={authStatus !== 'authenticated'}
              >
                Test
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* OneDrive Button Demo */}
      <Card>
        <CardHeader>
          <CardTitle>OneDrive Button Demo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              Test the OneDrive button component with different configurations:
            </p>
            
            <div className="flex flex-wrap gap-4">
              <OneDriveButton
                variant="default"
                onUploadSuccess={(response) => {
                  console.log('Upload successful:', response);
                }}
                onUploadError={(error) => {
                  console.error('Upload error:', error);
                }}
              />
              
              <OneDriveButton
                variant="outline"
                size="sm"
                onUploadSuccess={(response) => {
                  console.log('Upload successful:', response);
                }}
                onUploadError={(error) => {
                  console.error('Upload error:', error);
                }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Quick Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-gray-600">
            Run a complete test of the OneDrive integration including authentication and file upload.
          </p>

          <div className="flex gap-3">
            <OneDriveTestButton />
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">What this test does:</h4>
            <ol className="text-blue-800 text-sm list-decimal list-inside space-y-1">
              <li>Generates a device code for authentication</li>
              <li>Opens Microsoft verification page</li>
              <li>Waits for you to complete authentication</li>
              <li>Tests file upload to OneDrive</li>
              <li>Shows detailed results in browser console</li>
            </ol>
          </div>
        </CardContent>
      </Card>

      {/* Diagnostic Tool */}
      <OneDriveDiagnostic />

      {/* Debug Panel */}
      <OneDriveDebug />

      {/* Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle>Configuration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div><strong>Client ID:</strong> 86756722-ad2a-4ac0-8806-e2705653949a</div>
            <div><strong>Tenant ID:</strong> 14158288-a340-4380-88ed-a8989a932425</div>
            <div><strong>Redirect URI:</strong> http://localhost:8091/api/onedrive/callback</div>
            <div><strong>Scope:</strong> https://graph.microsoft.com/Files.ReadWrite.All</div>
            <div><strong>Base Path:</strong> /personal/prathamesh_kadam_redberyltech_com/Documents/RedBeryl</div>
          </div>

          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start gap-2">
              <Info className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-yellow-800">Azure Configuration Required</h4>
                <p className="text-yellow-700 text-sm mt-1">
                  If authentication fails, ensure the redirect URI is registered in Azure Portal:
                </p>
                <ol className="text-yellow-700 text-sm mt-2 list-decimal list-inside space-y-1">
                  <li>Go to <a href="https://portal.azure.com" target="_blank" rel="noopener noreferrer" className="underline">Azure Portal</a></li>
                  <li>Navigate to App registrations → Your app → Authentication</li>
                  <li>Add redirect URI: <code className="bg-yellow-100 px-1 rounded">http://localhost:8091/api/onedrive/callback</code></li>
                  <li>Save changes and try authentication again</li>
                </ol>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default OneDriveTest;
