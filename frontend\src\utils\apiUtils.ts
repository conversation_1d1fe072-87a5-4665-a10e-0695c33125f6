/**
 * Utility functions for API URLs
 */

// Test the function immediately
console.log('🧪 Testing getProxiedUrl function...');

/**
 * Converts a backend endpoint to a proxied URL that works with Vite's proxy configuration
 * @param endpoint The backend endpoint (e.g., '/clients/getAll', '/api/clients', etc.)
 * @returns A URL that will work with the Vite proxy
 */
export const getProxiedUrl = (endpoint: string): string => {
  // Remove leading slash if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;

  // Debug logging
  console.log(`🔧 getProxiedUrl: input="${endpoint}", clean="${cleanEndpoint}"`);

  // If the endpoint already starts with 'api/', 'candidates/', or 'redberyl-accounts/', use it as is
  if (cleanEndpoint.startsWith('api/') ||
      cleanEndpoint.startsWith('candidates/') ||
      cleanEndpoint.startsWith('redberyl-accounts/')) {
    console.log(`getProxiedUrl: using as-is for api/candidates/redberyl-accounts: /${cleanEndpoint}`);
    return `/${cleanEndpoint}`;
  }

  // Special handling for BDM endpoints - these don't need /api prefix
  if (cleanEndpoint.startsWith('bdms') ||
      cleanEndpoint.startsWith('v1/bdms') ||
      cleanEndpoint.startsWith('v1/')) {
    console.log(`getProxiedUrl: BDM/v1 endpoint detected, no /api prefix: /${cleanEndpoint}`);
    console.log(`getProxiedUrl: cleanEndpoint="${cleanEndpoint}", startsWith bdms: ${cleanEndpoint.startsWith('bdms')}, startsWith v1/bdms: ${cleanEndpoint.startsWith('v1/bdms')}, startsWith v1/: ${cleanEndpoint.startsWith('v1/')}`);
    return `/${cleanEndpoint}`;
  }

  // Otherwise, add the /api/ prefix for most endpoints
  console.log(`getProxiedUrl: adding /api prefix: /api/${cleanEndpoint}`);
  return `/api/${cleanEndpoint}`;
};

/**
 * Converts a list of backend endpoints to proxied URLs
 * @param endpoints List of backend endpoints
 * @returns List of proxied URLs
 */
export const getProxiedUrls = (endpoints: string[]): string[] => {
  return endpoints.map(endpoint => getProxiedUrl(endpoint));
};

/**
 * Authentication configuration
 */
export const AUTH_CONFIG = {
  username: 'admin',
  password: 'admin123'
};

/**
 * Creates a basic auth header
 * @returns The Authorization header value
 */
export const getBasicAuthHeader = (): string => {
  return 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
};

/**
 * Default headers for API requests
 */
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
};
