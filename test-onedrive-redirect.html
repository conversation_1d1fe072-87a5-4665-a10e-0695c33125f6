<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Redirect URI Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .button {
            background-color: #0078d4;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #106ebe;
        }
        .button.secondary {
            background-color: #6c757d;
        }
        .button.secondary:hover {
            background-color: #5a6268;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .result.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .config {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #0078d4;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 OneDrive Redirect URI Test</h1>
            <p>Test if your OneDrive redirect URI is working properly</p>
        </div>

        <div class="config">
            <h3>📋 Current Configuration</h3>
            <div><strong>Client ID:</strong> 86756722-ad2a-4ac0-8806-e2705653949a</div>
            <div><strong>Tenant ID:</strong> 14158288-a340-4380-88ed-a8989a932425</div>
            <div><strong>Redirect URI:</strong> http://localhost:8091/api/onedrive/callback</div>
            <div><strong>Scope:</strong> https://graph.microsoft.com/Files.ReadWrite.All</div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 1: Backend Endpoint Check</h3>
            <p>First, let's check if your backend OneDrive endpoint is running:</p>
            <button class="button" onclick="testBackendEndpoint()">Test Backend Endpoint</button>
            <div id="backend-result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 Test 2: OneDrive Authorization</h3>
            <p>Test the complete OneDrive authorization flow:</p>
            <button class="button" onclick="testOneDriveAuth()">Test OneDrive Authorization</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-section">
            <h3>📝 Instructions</h3>
            <div class="step">
                <strong>Step 1:</strong> Make sure your backend is running on port 8091
                <br><code>cd backend && mvn spring-boot:run</code>
            </div>
            <div class="step">
                <strong>Step 2:</strong> Add redirect URI to Azure Portal:
                <br>• Go to: <a href="https://portal.azure.com" target="_blank">https://portal.azure.com</a>
                <br>• Navigate: Azure Active Directory → App registrations
                <br>• Find app: 86756722-ad2a-4ac0-8806-e2705653949a
                <br>• Click: Authentication
                <br>• Add redirect URI: <code>http://localhost:8091/api/onedrive/callback</code>
                <br>• Save changes
            </div>
            <div class="step">
                <strong>Step 3:</strong> Test the authorization flow using the button above
            </div>
        </div>
    </div>

    <script>
        async function testBackendEndpoint() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '⏳ Testing backend endpoint...';
            
            try {
                const response = await fetch('http://localhost:8091/api/onedrive/test', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Backend is running!\n\nResponse: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Backend error: ${response.status} ${response.statusText}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Cannot reach backend: ${error.message}\n\nMake sure backend is running on port 8091`;
            }
        }

        async function testOneDriveAuth() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '⏳ Getting authorization URL...';
            
            try {
                // Get authorization URL from backend
                const response = await fetch('http://localhost:8091/api/onedrive/auth-url', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`Backend error: ${response.status}`);
                }

                const data = await response.json();
                
                if (!data.success || !data.authUrl) {
                    throw new Error(data.error || 'Failed to get authorization URL');
                }

                resultDiv.innerHTML = '🔗 Opening authorization popup...';

                // Open authorization popup
                const popup = window.open(
                    data.authUrl,
                    'onedrive-auth',
                    'width=600,height=700,scrollbars=yes,resizable=yes'
                );

                if (!popup) {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ Failed to open popup. Please allow popups for this site.';
                    return;
                }

                // Listen for popup messages
                window.addEventListener('message', function(event) {
                    if (event.origin !== window.location.origin && !event.origin.includes('localhost')) {
                        return;
                    }

                    if (event.data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = `✅ OneDrive authorization successful!\n\nAccess Token: ${event.data.accessToken.substring(0, 50)}...`;
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `❌ OneDrive authorization failed: ${event.data.error}`;
                    }
                }, { once: true });

                // Check if popup was closed without success
                const checkClosed = setInterval(() => {
                    if (popup.closed) {
                        clearInterval(checkClosed);
                        if (resultDiv.innerHTML.includes('Opening authorization popup')) {
                            resultDiv.className = 'result warning';
                            resultDiv.textContent = '⚠️ Authorization popup was closed. Please try again.';
                        }
                    }
                }, 1000);

            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
            }
        }
    </script>
</body>
</html>
