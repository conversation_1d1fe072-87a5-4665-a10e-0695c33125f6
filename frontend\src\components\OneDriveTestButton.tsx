import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { TestTube, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

const OneDriveTestButton: React.FC = () => {
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<'idle' | 'success' | 'error'>('idle');

  const runFullTest = async () => {
    setIsTesting(true);
    setTestResult('idle');

    try {
      console.clear();
      console.log('🧪 Starting OneDrive Integration Test');

      // Test 1: Device Code Flow
      console.log('📋 Test 1: Starting device code flow...');
      const deviceResponse = await fetch('/api/onedrive/device-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!deviceResponse.ok) {
        throw new Error(`Device code request failed: ${deviceResponse.status} ${deviceResponse.statusText}`);
      }

      const deviceData = await deviceResponse.json();
      console.log('📋 Device code response:', deviceData);

      if (!deviceData.success) {
        throw new Error(deviceData.error || 'Device code flow failed');
      }

      const { device_code, user_code, verification_uri } = deviceData;

      // Copy code to clipboard
      try {
        await navigator.clipboard.writeText(user_code);
        console.log('📋 Code copied to clipboard');
      } catch (e) {
        console.log('⚠️ Could not copy to clipboard');
      }

      // Show instructions
      const proceed = window.confirm(
        `OneDrive Test - Device Code Authentication\n\n` +
        `✅ Device code generated successfully!\n\n` +
        `CODE: ${user_code}\n` +
        `URL: ${verification_uri}\n\n` +
        `To complete the test:\n` +
        `1. Click OK to open Microsoft verification page\n` +
        `2. Enter the code: ${user_code}\n` +
        `3. Sign in with your Microsoft account\n` +
        `4. Return here to see the result\n\n` +
        `Click OK to continue or Cancel to stop test.`
      );

      if (!proceed) {
        console.log('❌ Test cancelled by user');
        toast.info('Test cancelled');
        return;
      }

      // Open verification page
      window.open(verification_uri, '_blank');

      toast.info('Authentication Test in Progress', {
        description: `Enter code ${user_code} on Microsoft page. Test will complete automatically.`,
        duration: 20000
      });

      // Test 2: Poll for Token
      console.log('🔄 Test 2: Polling for authentication...');
      let pollCount = 0;
      const maxPolls = 24; // 2 minutes at 5-second intervals
      let authSuccess = false;

      while (pollCount < maxPolls && !authSuccess) {
        await new Promise(resolve => setTimeout(resolve, 5000));
        pollCount++;

        console.log(`🔄 Poll attempt ${pollCount}/${maxPolls}...`);

        try {
          const tokenResponse = await fetch('/api/onedrive/device-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ device_code })
          });

          const tokenData = await tokenResponse.json();
          console.log(`📡 Poll ${pollCount} response:`, tokenData);

          if (tokenData.success && tokenData.access_token) {
            console.log('✅ Authentication successful!');
            localStorage.setItem('onedrive_access_token', tokenData.access_token);
            authSuccess = true;
            
            toast.success('Authentication Test Passed!', {
              description: 'OneDrive authentication is working correctly.'
            });
            break;
          } else if (tokenData.error === 'authorization_declined') {
            throw new Error('Authentication was declined');
          } else if (tokenData.error === 'expired_token') {
            throw new Error('Authentication code expired');
          } else if (tokenData.error === 'unauthorized') {
            throw new Error('Configuration error: Check Azure app credentials');
          } else if (tokenData.error !== 'authorization_pending') {
            console.error('Unexpected error:', tokenData);
            throw new Error(tokenData.message || tokenData.error || 'Authentication failed');
          }
          // Continue polling if authorization_pending
        } catch (pollError) {
          console.error('Polling error:', pollError);
          if (pollCount > 3) { // Only throw after a few attempts
            throw pollError;
          }
        }
      }

      if (!authSuccess) {
        throw new Error('Authentication timeout - test incomplete');
      }

      // Test 3: Upload Test
      console.log('📤 Test 3: Testing file upload...');
      
      // Create a test PDF
      const testPdfContent = `%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]/Contents 4 0 R>>endobj
4 0 obj<</Length 50>>stream
BT /F1 12 Tf 100 700 Td (OneDrive Test PDF) Tj ET
endstream endobj
xref 0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer<</Size 5/Root 1 0 R>>
startxref 300
%%EOF`;

      const testBlob = new Blob([testPdfContent], { type: 'application/pdf' });
      
      const formData = new FormData();
      formData.append('file', testBlob, 'OneDrive_Test.pdf');
      formData.append('invoiceNumber', 'TEST-001');

      const uploadResponse = await fetch('/api/onedrive/upload-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('onedrive_access_token')}`
        },
        body: formData
      });

      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        throw new Error(`Upload test failed: ${uploadResponse.status} - ${errorText}`);
      }

      const uploadResult = await uploadResponse.json();
      console.log('📤 Upload test result:', uploadResult);

      if (uploadResult.success) {
        console.log('✅ All tests passed!');
        setTestResult('success');
        toast.success('OneDrive Integration Test Passed!', {
          description: 'All components are working correctly. You can now save invoices to OneDrive.',
          action: uploadResult.webUrl ? {
            label: 'View Test File',
            onClick: () => window.open(uploadResult.webUrl, '_blank')
          } : undefined
        });
      } else {
        throw new Error(uploadResult.error || 'Upload test failed');
      }

    } catch (error) {
      console.error('❌ Test failed:', error);
      setTestResult('error');
      
      const errorMessage = error instanceof Error ? error.message : 'Test failed';
      toast.error('OneDrive Integration Test Failed', {
        description: errorMessage,
        duration: 10000
      });
    } finally {
      setIsTesting(false);
    }
  };

  const getIcon = () => {
    if (isTesting) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (testResult === 'success') return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (testResult === 'error') return <AlertCircle className="h-4 w-4 text-red-600" />;
    return <TestTube className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isTesting) return 'Testing...';
    if (testResult === 'success') return 'Test Passed';
    if (testResult === 'error') return 'Test Failed';
    return 'Test OneDrive Integration';
  };

  return (
    <Button
      onClick={runFullTest}
      disabled={isTesting}
      variant="outline"
      className="gap-2"
    >
      {getIcon()}
      {getButtonText()}
    </Button>
  );
};

export default OneDriveTestButton;
