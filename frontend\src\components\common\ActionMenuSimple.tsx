import React, { useState, useRef, useEffect } from 'react';
import { MoreHorizontal } from 'lucide-react';
import EditButton from './EditButton';
import ViewProjectButton from './ViewProjectButton';
import ViewInvoicesButton from './ViewInvoicesButton';
import DeleteButton from './DeleteButton';

interface ActionMenuSimpleProps {
  projectId: string | number;
  onEdit: (id: string | number) => void;
  onView: (id: string | number) => void;
  onViewInvoices?: (id: string | number) => void;
  onDelete: (id: string | number) => void;
}

const ActionMenuSimple: React.FC<ActionMenuSimpleProps> = ({
  projectId,
  onEdit,
  onView,
  onViewInvoices,
  onDelete
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Close the menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={menuRef}>
      {/* Trigger button */}
      <button
        type="button"
        className="inline-flex items-center justify-center rounded-md text-sm font-medium h-8 w-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300"
        onClick={() => setIsOpen(!isOpen)}
      >
        <MoreHorizontal className="h-4 w-4" />
        <span className="sr-only">Open menu</span>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-2 px-3 border-b border-gray-200">
            <div className="text-sm font-semibold">Actions</div>
          </div>
          <div className="py-1">
            {/* Edit button */}
            <EditButton
              onClick={() => {
                setIsOpen(false);
                onEdit(projectId);
              }}
            />

            {/* View Project button */}
            <ViewProjectButton
              onClick={() => {
                setIsOpen(false);
                onView(projectId);
              }}
            />

            {/* View Invoices button */}
            {onViewInvoices && (
              <ViewInvoicesButton
                onClick={() => {
                  setIsOpen(false);
                  onViewInvoices(projectId);
                }}
              />
            )}

            {/* Delete button */}
            <hr className="my-1 border-gray-200" />
            <DeleteButton
              onClick={() => {
                setIsOpen(false);
                onDelete(projectId);
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ActionMenuSimple;
