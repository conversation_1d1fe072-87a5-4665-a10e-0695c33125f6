����   = l
      java/lang/Object <init> ()V  TaxRateExample$TaxType
  
   
   java/lang/Long valueOf (J)Ljava/lang/Long;
     setId (Ljava/lang/Long;)V  GST
     
setTaxType (Ljava/lang/String;)V  Goods and Services Tax
     setTaxTypeDescription   TaxRateExample$TaxRate
  
   $ java/math/BigDecimal & 18.0
 # (  
  * + , setRate (Ljava/math/BigDecimal;)V
 . / 0 1 2 java/time/LocalDate of (III)Ljava/time/LocalDate;
  4 5 6 setEffectiveFrom (Ljava/time/LocalDate;)V
  8 9 6 setEffectiveTo
  ;  < (LTaxRateExample$TaxType;)V > +com/fasterxml/jackson/databind/ObjectMapper
 = 	 A B C D E 3com/fasterxml/jackson/databind/SerializationFeature 
INDENT_OUTPUT 5Lcom/fasterxml/jackson/databind/SerializationFeature;
 = G H I enable d(Lcom/fasterxml/jackson/databind/SerializationFeature;)Lcom/fasterxml/jackson/databind/ObjectMapper;
 = K L M findAndRegisterModules /()Lcom/fasterxml/jackson/databind/ObjectMapper;
 = O P Q writeValueAsString &(Ljava/lang/Object;)Ljava/lang/String;	 S T U V W java/lang/System out Ljava/io/PrintStream;
 Y Z [ \  java/io/PrintStream println ^ TaxRateExample Code LineNumberTable main ([Ljava/lang/String;)V 
Exceptions e java/lang/Exception 
SourceFile TaxRateExample.java NestMembers InnerClasses TaxType TaxRate ! ]           _        *� �    `        	 a b  _   �     ~� Y� 	L+
� 
� +� +� � Y� !M,
� 
� ",� #Y%� '� ),�� -� 3,�� -� 7,+� :� =Y� ?N-� @� FW-� JW-,� N:� R� X�    `   B             $  ,  9  F  T  Y  a  i  n " u # } $ c     d  f    g h       i      ] j 	  ] k 	