import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Cloud, CloudUpload, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import oneDriveService, { OneDriveUploadResponse } from '@/services/oneDriveService';
import { fetchInvoicePdfBlob, generateInvoicePdfBlob } from '@/utils/pdfUtils';
import { Invoice } from '@/types/invoice';

interface InvoiceOneDriveButtonProps {
  invoice: Invoice;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  onUploadSuccess?: (response: OneDriveUploadResponse) => void;
  onUploadError?: (error: string) => void;
}

const InvoiceOneDriveButton: React.FC<InvoiceOneDriveButtonProps> = ({
  invoice,
  variant = 'ghost',
  size = 'sm',
  className = '',
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleUpload = async () => {
    setIsUploading(true);
    setUploadStatus('idle');

    try {
      console.log('OneDrive upload started for invoice:', invoice.id);

      // Check if user is authenticated
      const authStatus = await oneDriveService.checkAuthentication();
      console.log('Authentication status:', authStatus);

      if (!authStatus.authenticated) {
        console.log('User not authenticated, starting authentication flow...');
        // Authenticate first
        const authResult = await oneDriveService.authenticate();

        if (!authResult.success) {
          throw new Error(authResult.error || 'Authentication failed');
        }

        toast.success('Successfully authenticated with OneDrive!');
      }

      // Generate PDF blob
      let pdfBlob: Blob;

      try {
        console.log('Attempting to fetch PDF from backend API for invoice:', invoice.id);
        // First try to fetch from backend API
        pdfBlob = await fetchInvoicePdfBlob(invoice.id);
        console.log('Successfully fetched PDF from backend API, size:', pdfBlob.size, 'bytes');
      } catch (backendError) {
        console.warn('Backend PDF fetch failed, generating client-side PDF:', backendError);

        try {
          // Fallback to client-side PDF generation
          console.log('Generating PDF client-side for invoice:', invoice);
          pdfBlob = await generateInvoicePdfBlob(invoice);
          console.log('Successfully generated PDF client-side, size:', pdfBlob.size, 'bytes');
        } catch (clientError) {
          console.error('Client-side PDF generation failed:', clientError);
          throw new Error('Failed to generate PDF: ' + (clientError instanceof Error ? clientError.message : String(clientError)));
        }
      }

      // Upload to OneDrive
      console.log('Uploading PDF to OneDrive, blob size:', pdfBlob.size, 'bytes');
      const response = await oneDriveService.uploadPdf(pdfBlob, invoice.id);
      console.log('OneDrive upload response:', response);

      if (response.success) {
        setUploadStatus('success');
        console.log('OneDrive upload successful:', response);
        toast.success('Invoice PDF saved to OneDrive!', {
          description: `File: ${response.fileName}`,
          action: response.webUrl ? {
            label: 'Open in OneDrive',
            onClick: () => window.open(response.webUrl, '_blank')
          } : undefined
        });

        if (onUploadSuccess) {
          onUploadSuccess(response);
        }
      } else {
        setUploadStatus('error');
        const errorMessage = response.error || response.message || 'Upload failed';
        console.error('OneDrive upload failed:', errorMessage);
        toast.error('Failed to save to OneDrive', {
          description: errorMessage
        });

        if (onUploadError) {
          onUploadError(errorMessage);
        }
      }
    } catch (error) {
      setUploadStatus('error');
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      console.error('OneDrive upload error:', error);
      
      toast.error('Failed to save to OneDrive', {
        description: errorMessage
      });
      
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    } finally {
      setIsUploading(false);
      // Reset status after 3 seconds
      setTimeout(() => setUploadStatus('idle'), 3000);
    }
  };

  const getButtonIcon = () => {
    if (isUploading) {
      return <Loader2 className="h-4 w-4 animate-spin" />;
    }
    
    if (uploadStatus === 'success') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    
    if (uploadStatus === 'error') {
      return <AlertCircle className="h-4 w-4 text-red-600" />;
    }
    
    return <CloudUpload className="h-4 w-4" />;
  };

  const getButtonText = () => {
    if (isUploading) {
      return 'Saving...';
    }
    
    if (uploadStatus === 'success') {
      return 'Saved';
    }
    
    if (uploadStatus === 'error') {
      return 'Failed';
    }
    
    return 'OneDrive';
  };

  const getButtonVariant = () => {
    if (uploadStatus === 'success') {
      return 'default';
    }
    
    if (uploadStatus === 'error') {
      return 'destructive';
    }
    
    return variant;
  };

  return (
    <Button
      onClick={handleUpload}
      disabled={isUploading}
      variant={getButtonVariant()}
      size={size}
      className={`${className} transition-all duration-200`}
      title={`Save Invoice ${invoice.id} to OneDrive`}
    >
      {getButtonIcon()}
      {size !== 'icon' && <span className="ml-1">{getButtonText()}</span>}
    </Button>
  );
};

export default InvoiceOneDriveButton;
