
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CustomSelect, SelectOption } from "@/components/ui/custom-select";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
import React, { useEffect, useState } from "react";
import { clientService, Client } from "@/services/clientService";
import { projectService } from "@/services/projectService";
import hsnCodeService from "@/services/hsnCodeService";
import { invoiceTypeService } from "@/services/invoiceTypeService";
import { directInvoiceTypeService, InvoiceType } from "@/services/directInvoiceTypeService";
import { simpleInvoiceTypeService, SimpleInvoiceType } from "@/services/simpleInvoiceTypeService";
import { publicInvoiceTypeService, PublicInvoiceType } from "@/services/publicInvoiceTypeService";
import { redberylAccountService, RedberylAccount } from "@/services/redberylAccountService";
import { staffingTypeService } from "@/services/staffingTypeService";
import candidateService from "@/services/candidateService";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";
import { Loader2 } from "lucide-react";

// Utility function to calculate working days in a month (hardcoded to 30 days)
const getWorkingDaysInMonth = (date: Date): number => {
  // Hardcoded to 30 working days per month as requested
  return 30;
};

// Helper function to format invoice number in financial year format
const formatInvoiceNumber = (invoiceId: string, invoiceDate: Date): string => {
  try {
    let numericPart;

    // Check if it's already in INV-XXX format
    if (invoiceId.startsWith('INV-')) {
      // Extract only the part after "INV-"
      numericPart = invoiceId.substring(4);
      // Remove any non-digits that might be there
      numericPart = numericPart.replace(/[^\d]/g, '');
    } else {
      // If it's just a number (like database ID), use it directly
      numericPart = invoiceId.replace(/[^\d]/g, '');
    }

    // Ensure it's at least 3 digits with leading zeros
    if (numericPart.length < 3) {
      numericPart = numericPart.padStart(3, '0');
    } else if (numericPart.length > 3) {
      // If it's longer than 3 digits, take only the last 3 digits
      // This handles cases where database ID gets mixed with invoice number
      numericPart = numericPart.slice(-3);
    }

    // Parse the invoice date
    const month = invoiceDate.getMonth() + 1; // getMonth() returns 0-11
    const year = invoiceDate.getFullYear();

    // Determine financial year (April to March)
    let fyStart, fyEnd;
    if (month >= 4) {
      // April to December - current FY
      fyStart = year;
      fyEnd = year + 1;
    } else {
      // January to March - previous FY
      fyStart = year - 1;
      fyEnd = year;
    }

    // Format as RB/YY-YY/XXX
    const fyStartShort = fyStart.toString().slice(-2);
    const fyEndShort = fyEnd.toString().slice(-2);

    return `RB/${fyStartShort}-${fyEndShort}/${numericPart}`;
  } catch (error) {
    console.error('Error formatting invoice number:', error);
    return invoiceId; // Fallback to original ID
  }
};

// Helper function to handle both string and number types
const flexibleId = z.union([
  z.string().min(1),
  z.number().int().positive()
]).transform(val => val.toString());

const formSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  clientId: flexibleId.refine(val => !!val, "Client is required"),
  projectId: flexibleId.refine(val => !!val, "Project is required"),
  candidateId: z.union([flexibleId, z.string().optional()]).optional(),
  invoiceTypeId: flexibleId.refine(val => !!val, "Invoice type is required"),
  staffingTypeId: z.union([flexibleId, z.string().optional()]).optional(),
  rate: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0 : num;
  }).optional(),
  billingAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  taxAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  totalAmount: z.union([z.string(), z.number()]).transform(val => {
    // Ensure it's a valid positive number
    const num = typeof val === 'string' ? parseFloat(val) : val;
    return isNaN(num) || num <= 0 ? 0.01 : num;
  }),
  issueDate: z.date(),
  dueDate: z.date(),
  isRecurring: z.boolean().default(false),
  publishedToFinance: z.boolean().default(false),
  publishedAt: z.date().optional(),
  hsnId: flexibleId.refine(val => !!val, "HSN code is required"),
  redberylAccountId: flexibleId.refine(val => !!val, "Redberyl account is required"),
  description: z.string().optional(),
  attendanceDays: z.union([z.string(), z.number()]).transform(val => {
    const num = typeof val === 'string' ? parseInt(val) : val;
    return isNaN(num) || num <= 0 ? 0 : num;
  }).refine(val => val > 0, "Attendance days is required and must be greater than 0"),
});

type FormValues = z.infer<typeof formSchema>;

interface InvoiceFormProps {
  invoice?: {
    id: string;
    databaseId?: number | string; // Add databaseId property
    client: string;
    project: string;
    candidate?: string;
    invoiceType?: string;
    staffingType?: string;
    amount: string;
    tax: string;
    total: string;
    issueDate: string;
    dueDate: string;
    status: string;
    recurring: boolean;
    publishedToFinance?: boolean;
    publishedAt?: string;
    hsnCode?: string;
    redberylAccount?: string;
    notes?: string;
  };
  onCancel: () => void;
  onSuccess: () => void;
}

// Error boundary component to catch rendering errors
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean, error: Error | null }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error("Error in InvoiceForm:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-6 bg-red-50 border border-red-200 rounded-lg">
          <h3 className="text-lg font-semibold text-red-700 mb-2">Something went wrong</h3>
          <p className="text-red-600 mb-4">
            {this.state.error?.message || "An unknown error occurred while rendering the form."}
          </p>
          <p className="text-sm text-gray-600 mb-4">
            Please try refreshing the page or contact support if the problem persists.
          </p>
          <div className="flex space-x-2">
            <button
              className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200"
              onClick={() => window.location.reload()}
            >
              Refresh Page
            </button>
            <button
              className="px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
              onClick={() => this.setState({ hasError: false, error: null })}
            >
              Try Again
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

const InvoiceForm: React.FC<InvoiceFormProps> = ({ invoice, onCancel, onSuccess }) => {
  const isEditing = !!invoice;

  console.log("InvoiceForm: Rendering with props:", {
    isEditing,
    invoice: invoice ? JSON.stringify(invoice) : null
  });

  // Validate invoice data if in edit mode
  if (isEditing && invoice) {
    console.log("Validating invoice data:", invoice);
    // Check for required fields
    if (!invoice.id) {
      console.error("Invoice is missing ID");
    }
    if (!invoice.client) {
      console.warn("Invoice is missing client");
    }
    if (!invoice.project) {
      console.warn("Invoice is missing project");
    }
  }

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      invoiceNumber: "",
      clientId: "",
      projectId: "",
      candidateId: "",
      invoiceTypeId: "",
      staffingTypeId: "",
      rate: 0,
      billingAmount: 0,
      taxAmount: 0,
      totalAmount: 0,
      issueDate: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      isRecurring: false,
      publishedToFinance: false,
      publishedAt: undefined,
      hsnId: "",
      redberylAccountId: "",
      description: "",
      attendanceDays: getWorkingDaysInMonth(new Date()),
    },
  });

  // Track if form has been initialized with dropdown values
  const [formInitialized, setFormInitialized] = useState(false);

  // State variables for dropdown data
  const [clients, setClients] = useState<Client[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [invoiceTypes, setInvoiceTypes] = useState<PublicInvoiceType[]>([]);
  const [staffingTypes, setStaffingTypes] = useState<any[]>([]);
  const [redberylAccounts, setRedberylAccounts] = useState<RedberylAccount[]>([]);
  const [hsnCodes, setHsnCodes] = useState<any[]>([]);
  const [candidates, setCandidates] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [invoiceTypesLoading, setInvoiceTypesLoading] = useState<boolean>(false);

  // Tax calculation state
  const [cgstAmount, setCgstAmount] = useState<number>(0);
  const [sgstAmount, setSgstAmount] = useState<number>(0);
  const [igstAmount, setIgstAmount] = useState<number>(0);
  const [isIntraState, setIsIntraState] = useState<boolean>(false);

  // Watch billing amount and project to auto-calculate tax and total
  const billingAmount = form.watch("billingAmount");
  const projectId = form.watch("projectId");

  // Watch rate, attendance days, and staffing type for auto-calculation
  const rate = form.watch("rate");
  const attendanceDays = form.watch("attendanceDays");
  const staffingTypeId = form.watch("staffingTypeId");
  const candidateId = form.watch("candidateId");

  // Comprehensive calculation function
  const performCalculations = React.useCallback(() => {
    console.log("=== PERFORMING CALCULATIONS ===");
    console.log("Current values:", {
      candidateId,
      staffingTypeId,
      attendanceDays,
      projectId,
      candidatesLength: candidates?.length,
      staffingTypesLength: staffingTypes?.length
    });

    if (candidateId && staffingTypeId && attendanceDays && candidates?.length > 0 && staffingTypes?.length > 0) {
      const selectedCandidate = candidates.find(c => c.id.toString() === candidateId);
      const selectedStaffingType = staffingTypes.find(st => st.id.toString() === staffingTypeId);

      if (selectedCandidate && selectedStaffingType && selectedCandidate.billingRate) {
        const monthlyRate = parseFloat(selectedCandidate.billingRate.toString());
        const attendanceDaysValue = parseInt(attendanceDays.toString());
        const staffingTypeName = selectedStaffingType.name?.toLowerCase() || '';

        // Check if it's monthly billing
        const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                               staffingTypeName.includes('contract') ||
                               staffingTypeName.includes('monthly');

        console.log("Calculation inputs:", {
          monthlyRate,
          attendanceDaysValue,
          isMonthlyBilling,
          staffingTypeName
        });

        if (isMonthlyBilling && monthlyRate > 0 && attendanceDaysValue > 0) {
          // Calculate billing amount: (monthly rate / 30) * attendance days
          const dailyRate = monthlyRate / 30;
          const calculatedBillingAmount = dailyRate * attendanceDaysValue;

          console.log("Monthly billing calculation:", {
            monthlyRate,
            dailyRate,
            attendanceDays: attendanceDaysValue,
            calculatedBillingAmount
          });

          // Update form values
          form.setValue("rate", monthlyRate, { shouldValidate: true, shouldDirty: true });
          form.setValue("billingAmount", calculatedBillingAmount, { shouldValidate: true, shouldDirty: true });

          // Calculate tax if project is selected
          if (projectId && projects?.length > 0) {
            const selectedProject = projects.find(p => p.id.toString() === projectId);
            if (selectedProject) {
              const projectState = selectedProject.state?.toLowerCase().trim() || '';
              const billingAddress = selectedProject.billingAddress?.toLowerCase().trim() || '';

              const isMaharashtra = projectState.includes('maharashtra') ||
                                  projectState.includes('mh') ||
                                  projectState === 'maharashtra' ||
                                  billingAddress.includes('maharashtra') ||
                                  billingAddress.includes('pune') ||
                                  billingAddress.includes('mumbai') ||
                                  billingAddress.includes('nagpur') ||
                                  billingAddress.includes('nashik') ||
                                  billingAddress.includes('aurangabad');

              let cgst = 0, sgst = 0, igst = 0, totalTax = 0;

              if (isMaharashtra) {
                cgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                sgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                totalTax = cgst + sgst;
                igst = 0;
              } else {
                igst = parseFloat((calculatedBillingAmount * 0.18).toFixed(2));
                totalTax = igst;
                cgst = 0;
                sgst = 0;
              }

              const total = parseFloat((calculatedBillingAmount + totalTax).toFixed(2));

              // Update state variables
              setCgstAmount(cgst);
              setSgstAmount(sgst);
              setIgstAmount(igst);
              setIsIntraState(isMaharashtra);

              // Update form tax values
              form.setValue("taxAmount", totalTax, { shouldValidate: true, shouldDirty: true });
              form.setValue("totalAmount", total, { shouldValidate: true, shouldDirty: true });

              console.log("Tax calculation completed:", {
                billingAmount: calculatedBillingAmount,
                isMaharashtra,
                cgst,
                sgst,
                igst,
                totalTax,
                total
              });
            }
          }

          console.log("=== CALCULATIONS COMPLETED ===");
          return true;
        }
      }
    }
    console.log("=== CALCULATIONS SKIPPED (missing data) ===");
    return false;
  }, [candidateId, staffingTypeId, attendanceDays, projectId, candidates, staffingTypes, projects, form]);

  // Trigger calculations when key values change
  useEffect(() => {
    console.log("useEffect triggered for calculations");
    performCalculations();
  }, [performCalculations]);

  // Additional trigger for when attendance days specifically changes
  useEffect(() => {
    if (attendanceDays) {
      console.log("Attendance days changed, triggering calculation:", attendanceDays);
      setTimeout(() => performCalculations(), 100);
    }
  }, [attendanceDays, performCalculations]);

  // Auto-calculate tax and total when billing amount or project changes
  useEffect(() => {
    // Only proceed if projects array is loaded and billingAmount exists
    if (billingAmount && projects && projects.length > 0) {
      try {
        const billing = parseFloat(billingAmount.toString());
        if (!isNaN(billing) && billing > 0) {
          // Safely determine if it's Maharashtra (intra-state) or other state (inter-state)
          const selectedProject = projectId ? projects.find(p => p.id.toString() === projectId) : null;
          // Enhanced Maharashtra detection logic
          const projectState = selectedProject?.state?.toLowerCase().trim() || '';
          const billingAddress = selectedProject?.billingAddress?.toLowerCase().trim() || '';

          const isMaharashtra = projectState.includes('maharashtra') ||
                              projectState.includes('mh') ||
                              projectState === 'maharashtra' ||
                              billingAddress.includes('maharashtra') ||
                              billingAddress.includes('pune') ||
                              billingAddress.includes('mumbai') ||
                              billingAddress.includes('nagpur') ||
                              billingAddress.includes('nashik') ||
                              billingAddress.includes('aurangabad');

          // Calculate tax based on state
          let cgst = 0, sgst = 0, igst = 0, totalTax = 0;

          if (isMaharashtra) {
            // Maharashtra: CGST (9%) + SGST (9%)
            cgst = parseFloat((billing * 0.09).toFixed(2));
            sgst = parseFloat((billing * 0.09).toFixed(2));
            totalTax = cgst + sgst;
            igst = 0;
          } else {
            // Other states: IGST (18%)
            igst = parseFloat((billing * 0.18).toFixed(2));
            totalTax = igst;
            cgst = 0;
            sgst = 0;
          }

          const total = parseFloat((billing + totalTax).toFixed(2));

          // Update state variables
          setCgstAmount(cgst);
          setSgstAmount(sgst);
          setIgstAmount(igst);
          setIsIntraState(isMaharashtra);

          // Update form values
          form.setValue("taxAmount", totalTax);
          form.setValue("totalAmount", total);

          // Enhanced debugging for tax calculation
          console.log('=== TAX CALCULATION DEBUG ===');
          console.log('Selected Project:', selectedProject);
          console.log('Project State:', selectedProject?.state);
          console.log('Project Billing Address:', selectedProject?.billingAddress);
          console.log('Is Maharashtra (Intra-State):', isMaharashtra);
          console.log('Tax Type:', isMaharashtra ? 'CGST+SGST (9%+9%)' : 'IGST (18%)');
          console.log('Billing Amount:', billing);
          console.log('CGST Amount:', cgst);
          console.log('SGST Amount:', sgst);
          console.log('IGST Amount:', igst);
          console.log('Total Tax Amount:', totalTax);
          console.log('Total Amount:', total);
          console.log('=============================');
        }
      } catch (e) {
        console.error("Error auto-calculating tax and total:", e);
      }
    }
  }, [billingAmount, projectId, projects, form]);





  // Initial form setup when editing an existing invoice
  useEffect(() => {
    if (invoice && !formInitialized &&
        clients.length > 0 && projects.length > 0 &&
        staffingTypes.length > 0 && redberylAccounts.length > 0 &&
        hsnCodes.length > 0 && !isLoading) {

      console.log("Setting initial form values for editing invoice:", invoice);
      console.log("Invoice project data details:", {
        project: invoice.project,
        projectId: (invoice as any).projectId,
        projectName: (invoice as any).projectName,
        invoiceType: typeof invoice.project,
        fullInvoiceData: invoice
      });
      console.log("Available dropdown data:", {
        clients: clients.length,
        projects: projects.length,
        staffingTypes: staffingTypes.length,
        redberylAccounts: redberylAccounts.length,
        hsnCodes: hsnCodes.length,
        candidates: candidates.length
      });

      // Debug the specific field values we're trying to map
      console.log("Invoice field values to map:", {
        staffingType: invoice.staffingType,
        hsnCode: invoice.hsnCode,
        redberylAccount: invoice.redberylAccount,
        candidate: invoice.candidate
      });

      // Parse amounts from string format - handle different currency formats
      const billingAmount = parseFloat(invoice.amount.replace(/[₹,]/g, '')) || 0;
      const taxAmount = parseFloat(invoice.tax.replace(/[₹,]/g, '')) || 0;
      const totalAmount = parseFloat(invoice.total.replace(/[₹SP,]/g, '')) || 0;

      console.log("Parsed amounts:", { billingAmount, taxAmount, totalAmount });

      // Initialize tax calculation state based on project
      const selectedProject = projects.find(p => p.name === invoice.project);
      if (selectedProject) {
        const projectState = selectedProject?.state?.toLowerCase().trim() || '';
        const billingAddress = selectedProject?.billingAddress?.toLowerCase().trim() || '';

        const isMaharashtra = projectState.includes('maharashtra') ||
                            projectState.includes('mh') ||
                            projectState === 'maharashtra' ||
                            billingAddress.includes('maharashtra') ||
                            billingAddress.includes('pune') ||
                            billingAddress.includes('mumbai') ||
                            billingAddress.includes('nagpur') ||
                            billingAddress.includes('nashik') ||
                            billingAddress.includes('aurangabad');

        if (isMaharashtra) {
          // Maharashtra: Split tax into CGST and SGST
          const cgst = taxAmount / 2;
          const sgst = taxAmount / 2;
          setCgstAmount(cgst);
          setSgstAmount(sgst);
          setIgstAmount(0);
          setIsIntraState(true);
        } else {
          // Other states: Use IGST
          setCgstAmount(0);
          setSgstAmount(0);
          setIgstAmount(taxAmount);
          setIsIntraState(false);
        }
      }

      // Set basic form values - use pre-formatted invoice number if available, otherwise format it
      const formattedInvoiceNumber = (invoice as any).formattedInvoiceNumber ||
                                    formatInvoiceNumber((invoice as any).invoiceNumber || invoice.id, new Date(invoice.issueDate));
      form.setValue('invoiceNumber', formattedInvoiceNumber);
      form.setValue('billingAmount', billingAmount);
      form.setValue('taxAmount', taxAmount);
      form.setValue('totalAmount', totalAmount);
      form.setValue('issueDate', new Date(invoice.issueDate));
      form.setValue('dueDate', new Date(invoice.dueDate));
      form.setValue('isRecurring', invoice.recurring);
      form.setValue('publishedToFinance', invoice.publishedToFinance || false);
      form.setValue('description', invoice.notes || "");

      // Set attendance days - use invoice data if available, otherwise default to current month working days
      const attendanceDaysFromInvoice = (invoice as any).attendanceDays || 0;
      const defaultAttendanceDays = attendanceDaysFromInvoice > 0 ? attendanceDaysFromInvoice : getWorkingDaysInMonth(new Date());
      form.setValue('attendanceDays', defaultAttendanceDays);
      console.log(`📅 Setting attendance days to: ${defaultAttendanceDays} (from invoice: ${attendanceDaysFromInvoice})`);

      // Set rate if available in invoice data
      const rateFromInvoice = (invoice as any).rate;
      if (rateFromInvoice) {
        const parsedRate = parseFloat(rateFromInvoice.toString()) || 0;
        form.setValue('rate', parsedRate);
        console.log(`💰 Setting rate from invoice data: ${parsedRate}`);
      } else {
        console.log(`⚠️ No rate found in invoice data, will auto-generate from candidate`);
      }

      // Map display names to IDs for dropdown values

      // Client dropdown - find ID by name
      if (invoice.client) {
        const clientMatch = clients.find(c => c.name === invoice.client);
        if (clientMatch) {
          console.log(`Setting clientId to ${clientMatch.id} for client ${invoice.client}`);
          form.setValue('clientId', clientMatch.id.toString());
        } else {
          console.warn(`Client not found: ${invoice.client}`);
        }
      }

      // Project dropdown - enhanced matching logic
      const invoiceAny = invoice as any;
      console.log("🔍 Looking for project in invoice data:", {
        project: invoice.project,
        projectId: invoiceAny.projectId,
        projectName: invoiceAny.projectName
      });
      console.log("📋 Available projects:", projects.map(p => ({ id: p.id, name: p.name })));

      let projectMatch = null;

      // Try multiple ways to find the project
      if (projects && projects.length > 0) {
        // Method 1: Try by projectId first (most reliable)
        if (invoiceAny.projectId) {
          projectMatch = projects.find(p => p.id.toString() === invoiceAny.projectId.toString());
          if (projectMatch) {
            console.log(`✅ Found project by ID: ${projectMatch.id} (${projectMatch.name})`);
          }
        }

        // Method 2: Try by project name if projectId didn't work
        if (!projectMatch && invoice.project) {
          projectMatch = projects.find(p => p.name === invoice.project);
          if (projectMatch) {
            console.log(`✅ Found project by name: ${projectMatch.id} (${projectMatch.name})`);
          }
        }

        // Method 3: Try by projectName field if available
        if (!projectMatch && invoiceAny.projectName) {
          projectMatch = projects.find(p => p.name === invoiceAny.projectName);
          if (projectMatch) {
            console.log(`✅ Found project by projectName: ${projectMatch.id} (${projectMatch.name})`);
          }
        }

        // Method 4: Try case-insensitive matching
        if (!projectMatch && invoice.project) {
          projectMatch = projects.find(p =>
            p.name.toLowerCase().trim() === invoice.project.toLowerCase().trim()
          );
          if (projectMatch) {
            console.log(`✅ Found project by case-insensitive name: ${projectMatch.id} (${projectMatch.name})`);
          }
        }

        // Set the project if found
        if (projectMatch) {
          form.setValue('projectId', projectMatch.id.toString());
          console.log(`🎯 Setting projectId to ${projectMatch.id} for project ${projectMatch.name}`);
        } else {
          console.warn(`❌ Project not found! Searched for:`, {
            project: invoice.project,
            projectId: invoiceAny.projectId,
            projectName: invoiceAny.projectName
          });
          console.log("Available project names:", projects.map(p => p.name));
        }
      }

      // Candidate dropdown - find ID by name
      if (invoice.candidate && invoice.candidate !== "-" && invoice.candidate !== null && invoice.candidate !== undefined) {
        console.log(`🔍 Looking for candidate: "${invoice.candidate}" in candidates:`, candidates);
        console.log(`📋 Available candidate names:`, candidates.map(c => c.name));
        console.log(`📊 Candidates length:`, candidates.length);

        const candidateMatch = candidates.find(c => c.name === invoice.candidate);
        if (candidateMatch) {
          console.log(`✅ Found candidate match! Setting candidateId to ${candidateMatch.id} for candidate ${invoice.candidate}`);
          console.log(`📊 Candidate billing rate: ${candidateMatch.billingRate}`);
          form.setValue('candidateId', candidateMatch.id.toString());

          // Immediately trigger rate generation if we have billing rate and rate is 0
          const invoiceRate = (invoice as any).rate;
          if (candidateMatch.billingRate && (!invoiceRate || invoiceRate === 0)) {
            console.log("🚀 Immediately generating rate from candidate billing rate");
            const billingRate = parseFloat(candidateMatch.billingRate.toString());
            form.setValue('rate', billingRate);

            // Calculate billing amount based on attendance days
            const attendanceDays = form.getValues('attendanceDays') || getWorkingDaysInMonth(new Date());
            const calculatedBillingAmount = parseFloat(((billingRate / 30) * attendanceDays).toFixed(2));
            form.setValue('billingAmount', calculatedBillingAmount);

            console.log("✅ Immediate rate generation complete:", {
              rate: billingRate,
              attendanceDays,
              billingAmount: calculatedBillingAmount
            });
          }
        } else {
          console.warn(`❌ Candidate not found: "${invoice.candidate}"`);
          console.log(`📋 Available candidates:`, candidates.map(c => ({ id: c.id, name: c.name, billingRate: c.billingRate })));
          // Don't set a fallback for candidate since it's optional
        }
      } else {
        console.log("ℹ️ No candidate in invoice data (candidate is empty, '-', null, or undefined), leaving candidate field empty");
        // Explicitly clear the candidate field to ensure it shows "Select candidate"
        form.setValue('candidateId', '');
      }

      // Invoice type dropdown - find ID by type name
      if (invoice.invoiceType) {
        const invoiceTypeMatch = invoiceTypes.find(it => it.invoiceType === invoice.invoiceType);
        if (invoiceTypeMatch) {
          console.log(`Setting invoiceTypeId to ${invoiceTypeMatch.id} for type ${invoice.invoiceType}`);
          form.setValue('invoiceTypeId', invoiceTypeMatch.id.toString());
        } else {
          console.warn(`Invoice type not found: ${invoice.invoiceType}`);
        }
      }

      // Staffing type dropdown - find ID by name
      if (invoice.staffingType && invoice.staffingType !== null) {
        console.log(`Looking for staffing type: "${invoice.staffingType}" in:`, staffingTypes.map(st => st.name));
        const staffingTypeMatch = staffingTypes.find(st => st.name === invoice.staffingType);
        if (staffingTypeMatch) {
          console.log(`Setting staffingTypeId to ${staffingTypeMatch.id} for staffing type ${invoice.staffingType}`);
          form.setValue('staffingTypeId', staffingTypeMatch.id.toString());
        } else {
          console.warn(`Staffing type not found: ${invoice.staffingType}`);
          // Try to find by partial match or set to first available
          const fallbackStaffingType = staffingTypes[0];
          if (fallbackStaffingType) {
            console.log(`Using fallback staffing type: ${fallbackStaffingType.name}`);
            form.setValue('staffingTypeId', fallbackStaffingType.id.toString());
          }
        }
      } else {
        console.log("No staffing type in invoice data, using first available");
        const fallbackStaffingType = staffingTypes[0];
        if (fallbackStaffingType) {
          console.log(`Using default staffing type: ${fallbackStaffingType.name}`);
          form.setValue('staffingTypeId', fallbackStaffingType.id.toString());
        }
      }

      // HSN code dropdown - find ID by code
      if (invoice.hsnCode) {
        console.log(`Looking for HSN code: "${invoice.hsnCode}" in:`, hsnCodes.map(h => h.code));
        const hsnMatch = hsnCodes.find(h => h.code === invoice.hsnCode);
        if (hsnMatch) {
          console.log(`Setting hsnId to ${hsnMatch.id} for HSN code ${invoice.hsnCode}`);
          form.setValue('hsnId', hsnMatch.id.toString());
        } else {
          console.warn(`HSN code not found: ${invoice.hsnCode}`);
          // Use first available HSN code as fallback
          const fallbackHsn = hsnCodes[0];
          if (fallbackHsn) {
            console.log(`Using fallback HSN code: ${fallbackHsn.code}`);
            form.setValue('hsnId', fallbackHsn.id.toString());
          }
        }
      } else {
        console.log("No HSN code in invoice data, using first available");
        const fallbackHsn = hsnCodes[0];
        if (fallbackHsn) {
          console.log(`Using default HSN code: ${fallbackHsn.code}`);
          form.setValue('hsnId', fallbackHsn.id.toString());
        }
      }

      // Redberyl account dropdown - find ID by name
      if (invoice.redberylAccount) {
        console.log(`Looking for redberyl account: "${invoice.redberylAccount}" in:`, redberylAccounts.map(ra => ra.name));
        const accountMatch = redberylAccounts.find(ra => ra.name === invoice.redberylAccount);
        if (accountMatch) {
          console.log(`Setting redberylAccountId to ${accountMatch.id} for account ${invoice.redberylAccount}`);
          form.setValue('redberylAccountId', accountMatch.id.toString());
        } else {
          console.warn(`Redberyl account not found: ${invoice.redberylAccount}`);
          // Try to find by partial match or set to first available
          const fallbackAccount = redberylAccounts[0];
          if (fallbackAccount) {
            console.log(`Using fallback redberyl account: ${fallbackAccount.name}`);
            form.setValue('redberylAccountId', fallbackAccount.id.toString());
          }
        }
      } else {
        console.log("No redberyl account in invoice data, using first available");
        const fallbackAccount = redberylAccounts[0];
        if (fallbackAccount) {
          console.log(`Using default redberyl account: ${fallbackAccount.name}`);
          form.setValue('redberylAccountId', fallbackAccount.id.toString());
        }
      }

      console.log("Finished setting form values for editing");

      // Auto-generate rate and billing amount after form is populated
      // Use shorter timeout for more immediate response
      setTimeout(() => {
        const candidateId = form.getValues('candidateId');
        const staffingTypeId = form.getValues('staffingTypeId');
        const currentRate = form.getValues('rate');
        const currentAttendanceDays = form.getValues('attendanceDays');
        const currentBillingAmount = form.getValues('billingAmount');

        // Use existing attendance days or default to current month working days
        const attendanceDays = currentAttendanceDays || getWorkingDaysInMonth(new Date());

        console.log("🔄 Auto-generating rate and billing amount for editing:", {
          candidateId,
          staffingTypeId,
          currentRate,
          currentAttendanceDays,
          attendanceDays,
          currentBillingAmount,
          candidatesAvailable: candidates.length,
          staffingTypesAvailable: staffingTypes.length
        });

        if (candidateId && candidates.length > 0 && staffingTypeId && staffingTypes.length > 0) {
          console.log("🔍 Searching for candidate and staffing type:", {
            candidateId,
            staffingTypeId,
            candidateIdType: typeof candidateId,
            staffingTypeIdType: typeof staffingTypeId,
            availableCandidates: candidates.map(c => ({ id: c.id, name: c.name, billingRate: c.billingRate })),
            availableStaffingTypes: staffingTypes.map(st => ({ id: st.id, name: st.name }))
          });

          // Debug: Check if we have the candidate data
          const candidateFound = candidates.find(c => c.id.toString() === candidateId.toString());
          console.log("🔍 Candidate lookup result:", {
            candidateId,
            candidateFound: candidateFound ? {
              id: candidateFound.id,
              name: candidateFound.name,
              billingRate: candidateFound.billingRate,
              billingRateType: typeof candidateFound.billingRate
            } : null
          });

          const selectedCandidate = candidates.find(c => c.id.toString() === candidateId.toString());
          const selectedStaffingType = staffingTypes.find(st => st.id.toString() === staffingTypeId.toString());

          console.log("🔍 Found candidate and staffing type:", {
            selectedCandidate: selectedCandidate ? { id: selectedCandidate.id, name: selectedCandidate.name, billingRate: selectedCandidate.billingRate } : null,
            selectedStaffingType: selectedStaffingType ? { id: selectedStaffingType.id, name: selectedStaffingType.name } : null
          });

          if (selectedCandidate && selectedStaffingType && selectedCandidate.billingRate) {
            const monthlyRate = parseFloat(selectedCandidate.billingRate.toString());
            const staffingTypeName = selectedStaffingType.name?.toLowerCase() || '';

            // Check if it's monthly billing
            const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                                   staffingTypeName.includes('contract') ||
                                   staffingTypeName.includes('monthly');

            console.log("📊 Auto-generation calculation:", {
              candidateName: selectedCandidate.name,
              monthlyRate,
              staffingTypeName,
              isMonthlyBilling,
              attendanceDays,
              needsRateGeneration: !currentRate || currentRate === 0,
              needsBillingAmountGeneration: !currentBillingAmount || currentBillingAmount === 0
            });

            if (isMonthlyBilling && monthlyRate > 0 && attendanceDays > 0) {
              // Force rate regeneration in editing mode if rate is 0 or missing
              const shouldRegenerateRate = !currentRate || currentRate === 0 ||
                                         (isEditing && (currentRate === 0 || currentRate !== monthlyRate));

              if (shouldRegenerateRate) {
                form.setValue("rate", monthlyRate, { shouldValidate: true });
                console.log("✅ Auto-generated rate (editing mode):", monthlyRate);
              }

              // Always set attendance days if missing
              if (!currentAttendanceDays) {
                form.setValue("attendanceDays", attendanceDays, { shouldValidate: true });
                console.log("✅ Auto-generated attendance days:", attendanceDays);
              }

              // Calculate billing amount: (monthly rate / 30) * attendance days
              const dailyRate = monthlyRate / 30;
              const calculatedBillingAmount = parseFloat((dailyRate * attendanceDays).toFixed(2));

              // Always recalculate billing amount for consistency
              form.setValue("billingAmount", calculatedBillingAmount, { shouldValidate: true });

              console.log("✅ Auto-generated values:", {
                rate: monthlyRate,
                dailyRate,
                attendanceDays,
                billingAmount: calculatedBillingAmount
              });

              // Trigger tax calculation
              const projectId = form.getValues('projectId');
              if (projectId && projects?.length > 0) {
                const selectedProject = projects.find(p => p.id.toString() === projectId);
                if (selectedProject) {
                  const projectState = selectedProject.state?.toLowerCase().trim() || '';
                  const billingAddress = selectedProject.billingAddress?.toLowerCase().trim() || '';

                  const isMaharashtra = projectState.includes('maharashtra') ||
                                      projectState.includes('mh') ||
                                      projectState === 'maharashtra' ||
                                      billingAddress.includes('maharashtra') ||
                                      billingAddress.includes('pune') ||
                                      billingAddress.includes('mumbai');

                  let cgst = 0, sgst = 0, igst = 0, totalTax = 0;

                  if (isMaharashtra) {
                    cgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                    sgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                    totalTax = cgst + sgst;
                    igst = 0;
                  } else {
                    igst = parseFloat((calculatedBillingAmount * 0.18).toFixed(2));
                    totalTax = igst;
                    cgst = 0;
                    sgst = 0;
                  }

                  const total = parseFloat((calculatedBillingAmount + totalTax).toFixed(2));

                  // Update tax state variables
                  setCgstAmount(cgst);
                  setSgstAmount(sgst);
                  setIgstAmount(igst);
                  setIsIntraState(isMaharashtra);

                  // Update form tax values
                  form.setValue("taxAmount", totalTax, { shouldValidate: true });
                  form.setValue("totalAmount", total, { shouldValidate: true });

                  console.log("💰 Auto-calculated taxes:", {
                    billingAmount: calculatedBillingAmount,
                    isMaharashtra,
                    cgst,
                    sgst,
                    igst,
                    totalTax,
                    total
                  });
                }
              }
            } else {
              console.log("⚠️ Skipping auto-generation: not monthly billing or missing data");
            }
          } else {
            console.log("⚠️ Missing candidate or staffing type data for auto-generation");
          }
        } else {
          console.log("⚠️ Missing required data for auto-generation");
        }
      }, 500); // Delay to ensure form is fully populated

      setFormInitialized(true);
    }
  }, [
    invoice,
    form,
    formInitialized,
    clients,
    projects,
    candidates,
    invoiceTypes,
    staffingTypes,
    hsnCodes,
    redberylAccounts,
    isLoading
  ]);

  const onSubmit = async (data: FormValues) => {
    console.log("Form submitted:", data);

    // Show a loading toast
    const loadingToast = toast.loading(isEditing ? "Updating invoice..." : "Creating invoice...");

    try {
      // Validate required fields
      if (!data.clientId) {
        toast.dismiss(loadingToast);
        toast.error("Client is required");
        return;
      }
      if (!data.projectId) {
        toast.dismiss(loadingToast);
        toast.error("Project is required");
        return;
      }
      if (!data.invoiceTypeId) {
        toast.dismiss(loadingToast);
        toast.error("Invoice Type is required");
        return;
      }

      // Auto-generate invoice number if not provided
      if (!data.invoiceNumber || data.invoiceNumber.trim() === '') {
        const timestamp = Date.now().toString().slice(-6);
        data.invoiceNumber = `INV-${timestamp}`;
        console.log("Auto-generated invoice number:", data.invoiceNumber);
      }

      // Find related objects for the invoice
      const client = clients && clients.length > 0 ? clients.find(c => c.id.toString() === data.clientId.toString()) : null;
      const project = projects && projects.length > 0 ? projects.find(p => p.id.toString() === data.projectId.toString()) : null;

      // Log the related objects for debugging
      console.log("InvoiceForm: Related objects for invoice submission:", {
        client,
        project,
        clientId: data.clientId,
        projectId: data.projectId,
        invoiceTypeId: data.invoiceTypeId,
        staffingTypeId: data.staffingTypeId,
        candidateId: data.candidateId,
        hsnId: data.hsnId,
        redberylAccountId: data.redberylAccountId
      });

      // Ensure numeric values are valid
      let billingAmount = 0;
      let taxAmount = 0;
      let totalAmount = 0;

      try {
        // Handle billing amount - remove currency symbols and commas
        if (typeof data.billingAmount === 'string') {
         // const cleanedValue = data.billingAmount.replace(/[₹$,]/g, '');
          billingAmount = parseFloat(data.billingAmount);
        } else {
          billingAmount = parseFloat(data.billingAmount.toString());
        }

        if (isNaN(billingAmount) || billingAmount <= 0) {
          billingAmount = 100.00; // Default value
        }

        console.log("Processed billing amount:", billingAmount);
      } catch (e) {
        console.error("Error processing billing amount:", e);
        billingAmount = 100.00; // Default value
      }

      // Calculate tax amount as 18% of billing amount
      try {
        // Handle tax amount - remove currency symbols and commas
        if (typeof data.taxAmount === 'string') {
          //const cleanedValue = data.taxAmount.replace(/[₹$,]/g, '');
          taxAmount = parseFloat(data.taxAmount);
        } else {
          taxAmount = parseFloat(data.taxAmount.toString());
        }

        if (isNaN(taxAmount) || taxAmount < 0) {
          // Always calculate tax as 18% of billing amount
          taxAmount = parseFloat((billingAmount * 0.18).toFixed(2));
        }

        console.log("Processed tax amount:", taxAmount);
      } catch (e) {
        console.error("Error processing tax amount:", e);
        taxAmount = parseFloat((billingAmount * 0.18).toFixed(2)); // Default value
      }

      // Calculate total amount as billing amount + tax amount
      try {
        // Handle total amount - remove currency symbols and commas
        if (typeof data.totalAmount === 'string') {
         // const cleanedValue = data.totalAmount.replace(/[₹$,]/g, '');
          totalAmount = parseFloat(data.totalAmount);
        } else {
          totalAmount = parseFloat(data.totalAmount.toString());
        }

        if (isNaN(totalAmount) || totalAmount <= 0) {
          // Always calculate total as billing + tax
          totalAmount = parseFloat((billingAmount + taxAmount).toFixed(2));
        }

        console.log("Processed total amount:", totalAmount);
      } catch (e) {
        console.error("Error processing total amount:", e);
        totalAmount = parseFloat((billingAmount + taxAmount).toFixed(2)); // Fallback calculation
      }

      // Convert form data to invoice object with proper type handling
      // Simplify the data structure to match what the backend expects
      const selectedCandidate = data.candidateId ? candidates.find(c => c.id.toString() === data.candidateId.toString()) : null;
      console.log("Selected candidate for invoice:", selectedCandidate);

      // Log all candidates for debugging
      console.log("All available candidates:", candidates);

      const invoiceData = {
        id: isEditing && invoice ? (invoice.databaseId || null) : null,
        invoiceNumber: data.invoiceNumber || `INV-${Date.now()}`,
        clientId: parseInt(data.clientId.toString()), // Send as number
        projectId: parseInt(data.projectId.toString()), // Send as number
        candidateId: data.candidateId ? parseInt(data.candidateId.toString()) : null,
        // Include candidate object if candidateId is provided
        candidate: selectedCandidate ? {
          id: parseInt(selectedCandidate.id.toString()),
          name: selectedCandidate.name
        } : null,
        invoiceTypeId: parseInt(data.invoiceTypeId.toString()), // Send as number
        staffingTypeId: data.staffingTypeId ? parseInt(data.staffingTypeId.toString()) : null,
        // Ensure numeric values are sent as numbers, not strings
        rate: data.rate ? Number(data.rate) : null,
        billingAmount: Number(billingAmount),
        taxAmount: Number(taxAmount),
        totalAmount: Number(totalAmount),
        invoiceDate: data.issueDate instanceof Date ? data.issueDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        dueDate: data.dueDate instanceof Date ? data.dueDate.toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        isRecurring: Boolean(data.isRecurring),
        publishedToFinance: Boolean(data.publishedToFinance),
        publishedAt: data.publishedAt ? data.publishedAt.toISOString() : null,
        hsnId: data.hsnId ? parseInt(data.hsnId.toString()) : 1,
        redberylAccountId: data.redberylAccountId ? parseInt(data.redberylAccountId.toString()) : 1,
        description: data.description || "",
        attendanceDays: data.attendanceDays || 0
      };

      console.log("Prepared invoice data for API:", invoiceData);

      // Import the invoice service
      const { invoiceService } = await import('@/services/invoiceService');

      try {
        // Call the API to save the invoice
        if (isEditing) {
          // Use the invoice database ID directly (should be a number)
          let numericId;

          // First try to use databaseId if available (this is the actual database ID)
          if (invoice.databaseId !== undefined && invoice.databaseId !== null) {
            if (typeof invoice.databaseId === 'number') {
              numericId = invoice.databaseId;
            } else if (typeof invoice.databaseId === 'string') {
              const parsedId = parseInt(invoice.databaseId);
              if (!isNaN(parsedId) && parsedId > 0) {
                numericId = parsedId;
              }
            }
          }

          // Fallback to invoice.id if databaseId is not available
          if (!numericId || numericId <= 0) {
            if (typeof invoice.id === 'number') {
              numericId = invoice.id;
            } else if (typeof invoice.id === 'string') {
              // Try to parse as number first
              const parsedId = parseInt(invoice.id);
              if (!isNaN(parsedId) && parsedId > 0) {
                numericId = parsedId;
              } else {
                // If it's a formatted ID like "INV-005", try to extract the number
                const match = invoice.id.match(/(\d+)$/);
                if (match) {
                  const extractedId = parseInt(match[1]);
                  if (!isNaN(extractedId) && extractedId > 0) {
                    numericId = extractedId;
                    console.log(`Extracted numeric ID ${extractedId} from formatted ID: ${invoice.id}`);
                  }
                }

                if (!numericId || numericId <= 0) {
                  toast.dismiss(loadingToast);
                  toast.error("Invalid invoice ID format. Cannot update invoice.");
                  console.error("Failed to parse numeric ID from:", invoice.id);
                  return;
                }
              }
            } else {
              toast.dismiss(loadingToast);
              toast.error("Invalid invoice ID. Cannot update invoice.");
              console.error("Invoice ID is not a number or string:", invoice.id);
              return;
            }
          }

          if (!numericId || isNaN(numericId) || numericId <= 0) {
            toast.dismiss(loadingToast);
            toast.error("Invalid invoice ID format. Cannot update invoice.");
            console.error("Failed to get valid numeric ID from:", {
              invoiceId: invoice.id,
              databaseId: invoice.databaseId,
              computedNumericId: numericId
            });
            return;
          }

          console.log(`Updating invoice: Display ID="${invoice.id}", Database ID="${invoice.databaseId}", Using Numeric ID=${numericId}`);

          const result = await invoiceService.updateInvoice(numericId, invoiceData);
          toast.dismiss(loadingToast);
          toast.success("Invoice updated successfully!");
          console.log("Update result:", result);

          // Call onSuccess to refresh the invoice list after successful update
          onSuccess();
          return;
        } else {
          console.log("Sending invoice data to API:", invoiceData);
          console.log("Candidate ID being sent:", invoiceData.candidateId);
          console.log("Candidate object being sent:", invoiceData.candidate);

          // Try the direct endpoint first
          try {
            const response = await fetch('http://localhost:8091/invoices/create-direct', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                clientId: invoiceData.clientId,
                projectId: invoiceData.projectId,
                candidateId: invoiceData.candidateId,
                candidate: invoiceData.candidate,
                invoiceTypeId: invoiceData.invoiceTypeId,
                invoiceNumber: invoiceData.invoiceNumber,
                billingAmount: invoiceData.billingAmount,
                taxAmount: invoiceData.taxAmount,
                totalAmount: invoiceData.totalAmount,
                invoiceDate: invoiceData.invoiceDate,
                dueDate: invoiceData.dueDate,
                isRecurring: invoiceData.isRecurring,
                publishedToFinance: invoiceData.publishedToFinance,
                hsnId: invoiceData.hsnId,
                redberylAccountId: invoiceData.redberylAccountId,
                description: invoiceData.description,
                attendanceDays: invoiceData.attendanceDays
              })
            });

            console.log("Direct API response status:", response.status);

            // Get the response text first
            const responseText = await response.text();
            console.log("Direct API response text:", responseText);

            if (response.ok) {
              try {
                // Try to parse as JSON if possible
                const result = responseText ? JSON.parse(responseText) : {};
                console.log("Create result:", result);
                toast.dismiss(loadingToast);
                toast.success(`Invoice ${result.invoiceNumber || ''} created successfully!`);
                onSuccess();
                return;
              } catch (parseError) {
                console.error("Error parsing JSON response:", parseError);
                // If we can't parse as JSON but the response was OK, still consider it a success
                toast.dismiss(loadingToast);
                toast.success("Invoice created successfully!");
                onSuccess();
                return;
              }
            } else {
              console.error("API Error response:", responseText);

              // Check if it's the specific ERROR response
              if (responseText.includes("ERROR-")) {
                throw new Error("Missing required fields. Please check all form values.");
              } else {
                // Try to parse the error response as JSON
                try {
                  const errorJson = JSON.parse(responseText);
                  if (errorJson.message) {
                    throw new Error(errorJson.message);
                  } else {
                    throw new Error(`Server error: ${responseText}`);
                  }
                } catch (parseError) {
                  throw new Error(`Server error: ${responseText}`);
                }
              }
            }
          } catch (directError: any) {
            console.error("Direct API call failed:", directError);

            // Fall back to the service method
            const result = await invoiceService.createInvoice(invoiceData);
            console.log("Create result from service:", result);
            toast.dismiss(loadingToast);

            if (result && result.id) {
              toast.success(`Invoice ${result.invoiceNumber || ''} created successfully!`);
            } else if (result && result.message && result.message.includes('locally')) {
              toast.warning("Invoice created locally. Server connection issue detected.");
            } else {
              toast.success("Invoice created successfully!");
            }
          }
        }

        // Call onSuccess to refresh the invoice list
        onSuccess();
      } catch (apiError: any) {
        console.error("API Error:", apiError);
        toast.dismiss(loadingToast);

        // Show a more detailed error message
        if (apiError.message && apiError.message.includes("Failed to fetch")) {
          toast.error("Could not connect to the server. Please check if the backend is running.");
        } else if (apiError.message && apiError.message.includes("validator")) {
          toast.error("Validation error. Please check the form values and try again.");
          console.error("Validation error details:", apiError);
        } else if (apiError.message && apiError.message.includes("Validation error") ||
                  apiError.message && apiError.message.includes("Missing required fields")) {
          toast.error(apiError.message);
        } else if (apiError.message && apiError.message.includes("Server error")) {
          toast.error(apiError.message);
        } else {
          toast.error(`Failed to ${isEditing ? "update" : "create"} invoice: ${apiError.message || "Unknown error"}`);
        }

        // Try to create a local invoice as fallback
        try {
          console.log("Attempting to create local invoice as fallback");
          const localInvoice = {
            id: Date.now().toString(),
            invoiceNumber: `INV-${Date.now()}`,
            client: client ? client.name : "Client " + data.clientId,
            project: project ? project.name : "Project " + data.projectId,
            amount: `$${billingAmount.toFixed(2)}`,
            tax: `$${taxAmount.toFixed(2)}`,
            total: `$${totalAmount.toFixed(2)}`,
            issueDate: data.issueDate instanceof Date ? data.issueDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
            dueDate: data.dueDate instanceof Date ? data.dueDate.toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            status: "Draft",
            recurring: Boolean(data.isRecurring),
            notes: data.description || ""
          };

          // Store in local storage for persistence
          const localInvoices = JSON.parse(localStorage.getItem('localInvoices') || '[]');
          localInvoices.push(localInvoice);
          localStorage.setItem('localInvoices', JSON.stringify(localInvoices));

          toast.info("Created a local draft invoice as fallback");
          onSuccess();
        } catch (localError) {
          console.error("Failed to create local invoice:", localError);
        }
      }
    } catch (error: any) {
      console.error("Error saving invoice:", error);
      toast.dismiss(loadingToast);
      toast.error(`Failed to save invoice: ${error.message || "Unknown error"}`);
    }
  };

  // Removed handleGenerateInvoice function as Generate Invoice button was removed

  // State variables for dropdown data (moved to avoid hoisting issues)
  // These are now declared earlier in the component

  // Dedicated useEffect for invoice types using the public service
  useEffect(() => {
    const fetchInvoiceTypes = async () => {
      setInvoiceTypesLoading(true);
      console.log("InvoiceForm: Starting invoice type fetch with public service");

      try {
        // Use the public service which directly accesses the database
        console.log("InvoiceForm: Fetching invoice types with public service...");
        const invoiceTypesData = await publicInvoiceTypeService.getAllInvoiceTypes();

        if (invoiceTypesData && invoiceTypesData.length > 0) {
          console.log("InvoiceForm: Successfully fetched invoice types with public service:", invoiceTypesData);
          setInvoiceTypes(invoiceTypesData);
          toast.success("Invoice types loaded from database");
        } else {
          throw new Error("No invoice types found in database");
        }
      } catch (error) {
        console.error("InvoiceForm: Error fetching invoice types with public service:", error);

        // If public service fails, try the simple service
        try {
          console.log("InvoiceForm: Trying simple service as fallback...");
          const simpleData = await simpleInvoiceTypeService.getAllInvoiceTypes();

          if (simpleData && simpleData.length > 0) {
            // Convert to PublicInvoiceType format
            const convertedData = simpleData.map(item => ({
              id: item.id.toString(),
              invoiceType: item.name,
              typeDesc: item.description
            }));

            console.log("InvoiceForm: Successfully fetched with simple service:", convertedData);
            setInvoiceTypes(convertedData);
            toast.info("Using alternative source for invoice types");
          } else {
            throw new Error("No invoice types found from simple service");
          }
        } catch (simpleError) {
          console.error("InvoiceForm: Error fetching with simple service:", simpleError);

          // If simple service fails, try direct service
          try {
            console.log("InvoiceForm: Trying direct service as last resort...");
            const directData = await directInvoiceTypeService.getAllInvoiceTypes();

            if (directData && directData.length > 0) {
              // Convert to PublicInvoiceType format
              const convertedData = directData.map(item => ({
                id: item.id.toString(),
                invoiceType: item.name || item.invoiceType || "Unknown",
                typeDesc: item.description || item.typeDesc || ""
              }));

              console.log("InvoiceForm: Successfully fetched with direct service:", convertedData);
              setInvoiceTypes(convertedData);
              toast.info("Using fallback source for invoice types");
            } else {
              throw new Error("No invoice types found from direct service");
            }
          } catch (directError) {
            console.error("InvoiceForm: All services failed:", directError);

            // Fallback to hardcoded data as a last resort
            const hardcodedTypes = [
              { id: "1", invoiceType: "Standard", typeDesc: "Regular invoice for services or products" },
              { id: "2", invoiceType: "Proforma", typeDesc: "Preliminary bill of sale sent to buyers in advance of a shipment or delivery" },
              { id: "3", invoiceType: "Credit Note", typeDesc: "Document issued to indicate a return of funds" },
              { id: "4", invoiceType: "Debit Note", typeDesc: "Document issued to request additional payment" }
            ];

            console.log("InvoiceForm: Using hardcoded invoice types:", hardcodedTypes);
            setInvoiceTypes(hardcodedTypes);
            toast.warning("Using default invoice types. Could not connect to database.");
          }
        }
      } finally {
        setInvoiceTypesLoading(false);
      }
    };

    fetchInvoiceTypes();
  }, []);



  // Fetch data for all other dropdowns
  useEffect(() => {
    const fetchDropdownData = async () => {
      setIsLoading(true);
      try {
        // Fetch clients
        const clientsData = await clientService.getAllClients();
        setClients(clientsData);
        console.log("Fetched clients:", clientsData);

        // Fetch projects
        try {
          console.log("InvoiceForm: Fetching projects...");
          const projectsData = await projectService.getAllProjects();

          if (projectsData && projectsData.length > 0) {
            // Ensure IDs are strings for the dropdown and client data is properly structured
            const formattedProjects = projectsData.map(project => {
              // Process client data
              let clientData = project.client;

              // If client is not present but clientId is, try to find it in the clients array first
              if (!clientData && project.clientId) {
                // First try to find the client in the already loaded clients array
                const existingClient = clients.find(c => c.id.toString() === project.clientId.toString());
                if (existingClient) {
                  clientData = existingClient;
                  console.log(`InvoiceForm: Found client in loaded array for project ${project.id}:`, clientData);
                } else {
                  // Create a placeholder client object
                  clientData = { id: project.clientId, name: "Loading..." };
                  console.log(`InvoiceForm: Created placeholder client for project ${project.id}:`, clientData);
                }

                // Try to fetch client data asynchronously
                (async () => {
                  try {
                    const clientResponse = await fetch(`/api/clients/${project.clientId}`);
                    if (clientResponse.ok) {
                      const clientInfo = await clientResponse.json();
                      console.log(`InvoiceForm: Fetched client info for project ${project.id}:`, clientInfo);

                      // Update the project with the client info
                      setProjects(prevProjects =>
                        prevProjects.map(p =>
                          p.id.toString() === project.id.toString()
                            ? { ...p, client: clientInfo }
                            : p
                        )
                      );
                    }
                  } catch (error) {
                    console.error(`InvoiceForm: Failed to fetch client info for project ${project.id}:`, error);
                  }
                })();
              }

              return {
                ...project,
                id: project.id.toString(),
                clientId: project.clientId, // Ensure clientId is preserved
                client: clientData
              };
            });

            setProjects(formattedProjects);
            console.log("InvoiceForm: Successfully fetched projects:", formattedProjects);
            console.log("InvoiceForm: Project client data check:", formattedProjects.map(p => ({
              id: p.id,
              name: p.name,
              clientId: p.clientId,
              client: p.client
            })));
            toast.success("Projects loaded successfully");
          } else {
            console.warn("InvoiceForm: No projects returned from API");
            throw new Error("No projects found");
          }
        } catch (error) {
          console.error("InvoiceForm: Error fetching projects:", error);

          // Try direct fetch as fallback
          try {
            console.log("InvoiceForm: Trying direct fetch for projects...");
            const response = await fetch('/projects/getAll', {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              const data = await response.json();
              console.log("InvoiceForm: Direct fetch projects response:", data);

              if (Array.isArray(data) && data.length > 0) {
                // Ensure IDs are strings for the dropdown and client data is properly structured
                const formattedProjects = data.map(project => {
                  // Process client data
                  let clientData = project.client;

                  // If client is not present but clientId is, create a placeholder client object
                  if (!clientData && project.clientId) {
                    clientData = { id: project.clientId, name: "Loading..." };

                    // Try to fetch client data asynchronously
                    (async () => {
                      try {
                        const clientResponse = await fetch(`/api/clients/${project.clientId}`);
                        if (clientResponse.ok) {
                          const clientInfo = await clientResponse.json();
                          console.log(`InvoiceForm: Fetched client info for project ${project.id}:`, clientInfo);

                          // Update the project with the client info
                          setProjects(prevProjects =>
                            prevProjects.map(p =>
                              p.id.toString() === project.id.toString()
                                ? { ...p, client: clientInfo }
                                : p
                            )
                          );
                        }
                      } catch (error) {
                        console.error(`InvoiceForm: Failed to fetch client info for project ${project.id}:`, error);
                      }
                    })();
                  }

                  return {
                    ...project,
                    id: project.id.toString(),
                    client: clientData
                  };
                });

                setProjects(formattedProjects);
                console.log("InvoiceForm: Successfully fetched projects via direct fetch:", formattedProjects);
                toast.info("Projects loaded via alternative method");
                return;
              }
            }

            throw new Error("Direct fetch failed or returned no data");
          } catch (directError) {
            console.error("InvoiceForm: Direct fetch for projects failed:", directError);

            // No fallback to mock data - leave projects empty
            setProjects([]);
            console.log("InvoiceForm: No projects available - API connection failed");
            toast.error("Could not load projects. Please check your connection and try again.");
          }
        }

        // Fetch staffing types
        try {
          const staffingTypesData = await staffingTypeService.getAllStaffingTypes();
          if (staffingTypesData && staffingTypesData.length > 0) {
            setStaffingTypes(staffingTypesData);
            console.log("Fetched staffing types:", staffingTypesData);
          } else {
            throw new Error("No staffing types returned");
          }
        } catch (error) {
          console.error("Error fetching staffing types:", error);
          // Fallback to mock data with proper structure
          const fallbackStaffingTypes = [
            { id: "1", name: "Full-time", description: "Regular full-time employment" },
            { id: "2", name: "Part-time", description: "Regular part-time employment" },
            { id: "3", name: "Contract", description: "Fixed-term contract employment" },
            { id: "4", name: "Temporary", description: "Short-term temporary employment" },
          ];
          setStaffingTypes(fallbackStaffingTypes);
          console.log("Using fallback staffing types:", fallbackStaffingTypes);
        }

        // Fetch HSN codes
        try {
          const hsnCodesData = await hsnCodeService.getAllHsnCodes();
          setHsnCodes(hsnCodesData);
          console.log("Fetched HSN codes:", hsnCodesData);
        } catch (error) {
          console.error("Error fetching HSN codes:", error);
          // Fallback to mock data
          setHsnCodes([
            { id: "1", code: "998313" },
            { id: "2", code: "998314" },
            { id: "3", code: "998315" },
          ]);
        }

        // Fetch Redberyl accounts
        try {
          console.log("Attempting to fetch Redberyl accounts...");
          const redberylAccountsData = await redberylAccountService.getAllRedberylAccounts();

          if (redberylAccountsData && redberylAccountsData.length > 0) {
            console.log("Successfully fetched Redberyl accounts:", redberylAccountsData);

            // Ensure IDs are strings for the dropdown and add a name if missing
            const formattedAccounts = redberylAccountsData.map(account => ({
              ...account,
              id: account.id.toString(),
              name: account.name || account.accountName || `Account ${account.id} (${account.bankName || 'Unknown Bank'})`
            }));

            setRedberylAccounts(formattedAccounts);
            toast.success("Redberyl accounts loaded successfully");
          } else {
            console.warn("Redberyl accounts data was empty or invalid");
            throw new Error("Empty or invalid Redberyl accounts data");
          }
        } catch (error) {
          console.error("Error fetching Redberyl accounts:", error);
          console.log("Using fallback Redberyl accounts data");

          // Fallback to enhanced mock data with proper structure
          const fallbackAccounts = [
            {
              id: "1",
              name: "HDFC Main Account",
              bankName: "HDFC Bank",
              accountNo: "**************",
              ifscCode: "HDFC0001234",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL001",
              costCenter: "CC001"
            },
            {
              id: "2",
              name: "ICICI Secondary Account",
              bankName: "ICICI Bank",
              accountNo: "**************",
              ifscCode: "ICIC0005678",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL002",
              costCenter: "CC002"
            },
            {
              id: "3",
              name: "SBI Payroll Account",
              bankName: "State Bank of India",
              accountNo: "**************",
              ifscCode: "SBIN0012345",
              accountName: "RedBeryl Tech Solutions Pvt Ltd",
              glCode: "GL003",
              costCenter: "CC003"
            },
          ];

          console.log("Using fallback Redberyl accounts:", fallbackAccounts);
          setRedberylAccounts(fallbackAccounts);
        }

        // Fetch candidates
        try {
          console.log("🔄 Starting candidate fetch...");
          const candidatesData = await candidateService.getAllCandidates();
          console.log("📋 Fetched candidates from service:", candidatesData);

          if (candidatesData && candidatesData.length > 0) {
            console.log("✅ Setting candidates from service:", candidatesData.length, "candidates");
            setCandidates(candidatesData);
          } else {
            console.warn("⚠️ No candidates returned from service, trying alternative endpoints");

            // Try multiple alternative endpoints
            const alternativeEndpoints = [
              '/api/debug/candidates',
              '/api/candidates',
              'http://localhost:8091/candidates/getAll',
              'http://localhost:8091/debug/candidates'
            ];

            let candidatesFound = false;

            for (const endpoint of alternativeEndpoints) {
              try {
                console.log(`🔄 Trying endpoint: ${endpoint}`);
                const response = await fetch(endpoint, {
                  credentials: 'include',
                  headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                  }
                });

                if (response.ok) {
                  const debugCandidates = await response.json();
                  console.log(`✅ Fetched candidates from ${endpoint}:`, debugCandidates);

                  if (debugCandidates && Array.isArray(debugCandidates) && debugCandidates.length > 0) {
                    // Convert to expected format with billing rate
                    const formattedCandidates = debugCandidates.map((c: any) => ({
                      id: c.id?.toString() || '0',
                      name: c.name || "Unknown Candidate",
                      billingRate: c.billingRate || 0
                    }));

                    console.log(`📋 Setting formatted candidates from ${endpoint}:`, formattedCandidates);
                    setCandidates(formattedCandidates);
                    candidatesFound = true;
                    break;
                  }
                } else {
                  console.warn(`❌ Endpoint ${endpoint} returned status: ${response.status}`);
                }
              } catch (endpointError) {
                console.error(`❌ Error fetching from ${endpoint}:`, endpointError);
              }
            }

            if (!candidatesFound) {
              console.warn("⚠️ All candidate endpoints failed");
              // No fallback to mock data - leave candidates empty
              setCandidates([]);
              console.log("InvoiceForm: No candidates available - API connection failed");
              toast.error("Could not load candidates. Please check your connection and try again.");
            }
          }
        } catch (error) {
          console.error("❌ Error fetching candidates:", error);
          // No fallback to mock data - leave candidates empty
          setCandidates([]);
          console.log("InvoiceForm: No candidates available - API connection failed");
          toast.error("Could not load candidates. Please check your connection and try again.");
        }
      } catch (error) {
        console.error("Error fetching dropdown data:", error);
        toast.error("Failed to load some dropdown data. Using fallback values.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDropdownData();
  }, []);

  return (
    <Card>
      <CardContent className="p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {isLoading && (
              <div className="flex items-center justify-center py-4 mb-4 bg-gray-50 rounded-md">
                <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
                <p className="text-sm text-muted-foreground">Loading additional form data...</p>
              </div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="invoiceNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Invoice Number</FormLabel>
                    <FormControl>
                      <Input placeholder="INV-001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="clientId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Client</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={clients.map(client => ({
                          value: client.id,
                          label: client.name
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select client"
                        error={!!form.formState.errors.clientId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="projectId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Project
                      {isLoading && (
                        <Loader2 className="ml-2 h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </FormLabel>
                    <FormControl>
                      {isLoading ? (
                        <div className="flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
                          <span>Loading projects...</span>
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        <CustomSelect
                          options={projects.map(project => {
                            console.log("Mapping project for dropdown:", project);
                            // Extract client name from the project data
                            const clientName = project.client?.name ||
                                             (project.client ? project.client.name : 'Unknown');

                            return {
                              value: project.id.toString(),
                              label: project.name || "Unnamed Project",
                              description: `Client: ${clientName}`
                            };
                          })}
                          value={field.value}
                          onChange={(value) => {
                            console.log("Project selected:", value);
                            field.onChange(value);

                            // Auto-populate client when project is selected
                            if (projects && projects.length > 0) {
                              const selected = projects.find(p => p.id.toString() === value);
                              if (selected) {
                                console.log("Selected project details:", selected);
                                console.log("Available clients for matching:", clients.map(c => ({ id: c.id, name: c.name })));

                                // Auto-fill client from project data
                                if (selected.client && selected.client.id) {
                                  console.log("Auto-filling client from project.client:", selected.client.id, selected.client.name);
                                  form.setValue('clientId', selected.client.id.toString());
                                  toast.success(`Auto-selected client: ${selected.client.name}`);
                                } else if (selected.clientId) {
                                  console.log("Auto-filling client from project.clientId:", selected.clientId);
                                  form.setValue('clientId', selected.clientId.toString());

                                  // Find client name for toast
                                  const clientMatch = clients.find(c => c.id.toString() === selected.clientId.toString());
                                  if (clientMatch) {
                                    toast.success(`Auto-selected client: ${clientMatch.name}`);
                                  } else {
                                    console.warn("Client not found in clients array for clientId:", selected.clientId);
                                    toast.info("Client auto-selected (ID: " + selected.clientId + ")");
                                  }
                                } else {
                                  console.warn("No client data found in selected project:", selected);
                                  toast.warning("No client information available for this project");
                                }

                                toast.info(`Selected project: ${selected.name}`);
                              } else {
                                console.error("Project not found for value:", value);
                              }
                            } else {
                              console.error("No projects available for auto-population");
                            }
                          }}
                          placeholder="Select project"
                          error={!!form.formState.errors.projectId}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                    {!isLoading && projects.length === 0 && (
                      <p className="text-xs text-red-500 mt-1">
                        No projects available. Please check your connection.
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="issueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Issue Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dueDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          className={cn("p-3 pointer-events-auto")}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="invoiceTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center">
                      Invoice Type
                      {invoiceTypesLoading && (
                        <Loader2 className="ml-2 h-3 w-3 animate-spin text-muted-foreground" />
                      )}
                    </FormLabel>
                    <FormControl>
                      {invoiceTypesLoading ? (
                        <div className="flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
                          <span>Loading invoice types...</span>
                          <Loader2 className="h-4 w-4 animate-spin" />
                        </div>
                      ) : (
                        <CustomSelect
                          options={invoiceTypes.map(type => {
                            console.log("Mapping invoice type for dropdown:", type);
                            return {
                              value: type.id.toString(),
                              label: type.invoiceType,
                              description: type.typeDesc
                            };
                          })}
                          value={field.value}
                          onChange={(value) => {
                            console.log("Invoice type selected:", value);
                            field.onChange(value);

                            // Log the selected invoice type for debugging
                            const selected = invoiceTypes.find(t => t.id.toString() === value);
                            if (selected) {
                              console.log("Selected invoice type details:", selected);
                              toast.info(`Selected invoice type: ${selected.invoiceType}`);
                            }
                          }}
                          placeholder="Select invoice type"
                          error={!!form.formState.errors.invoiceTypeId}
                        />
                      )}
                    </FormControl>
                    <FormMessage />
                    {!invoiceTypesLoading && invoiceTypes.length === 0 && (
                      <p className="text-xs text-red-500 mt-1">
                        No invoice types available. Please check your connection.
                      </p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="staffingTypeId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Staffing Type</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={staffingTypes.map(type => ({
                          value: type.id,
                          label: type.name
                        }))}
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value);

                          // Auto-generate rate and billing amount when staffing type changes
                          const candidateId = form.getValues('candidateId');
                          if (candidateId && candidates.length > 0) {
                            const selectedCandidate = candidates.find(c => c.id.toString() === candidateId);
                            if (selectedCandidate && selectedCandidate.billingRate) {
                              const billingRate = parseFloat(selectedCandidate.billingRate.toString());
                              const attendanceDays = form.getValues('attendanceDays') || getWorkingDaysInMonth(new Date());

                              const selectedStaffingType = staffingTypes.find(st => st.id.toString() === value);
                              const staffingTypeName = selectedStaffingType?.name?.toLowerCase() || '';

                              // Check if it's a monthly billing type
                              const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                                                     staffingTypeName.includes('contract') ||
                                                     staffingTypeName.includes('monthly');

                              let calculatedRate = 0;
                              let calculatedBillingAmount = 0;

                              if (isMonthlyBilling) {
                                // For monthly billing: rate = billing rate (monthly rate)
                                calculatedRate = billingRate;
                                // Billing amount = (monthly rate ÷ 30) × attendance days
                                calculatedBillingAmount = parseFloat(((billingRate / 30) * attendanceDays).toFixed(2));
                              } else {
                                // For daily/hourly billing: rate = billing rate ÷ attendance days
                                calculatedRate = attendanceDays > 0 ? billingRate / attendanceDays : billingRate;
                                // Billing amount = rate × attendance days
                                calculatedBillingAmount = parseFloat((calculatedRate * attendanceDays).toFixed(2));
                              }

                              console.log("🔄 Auto-generating on staffing type change:", {
                                candidateName: selectedCandidate.name,
                                billingRate,
                                staffingType: staffingTypeName,
                                isMonthlyBilling,
                                attendanceDays,
                                calculatedRate,
                                calculatedBillingAmount
                              });

                              form.setValue('rate', calculatedRate);
                              form.setValue('billingAmount', calculatedBillingAmount);
                            }
                          }
                        }}
                        placeholder="Select staffing type"
                        error={!!form.formState.errors.staffingTypeId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Rate *
                      <span className="ml-2 text-xs text-gray-500">
                        (Auto-generated from candidate billing rate based on staffing type)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <div className="flex gap-2">
                        <Input
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                            // Auto-calculate billing amount when rate changes
                          }}
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Force regenerate rate from candidate billing rate
                            const candidateId = form.getValues('candidateId');
                            const staffingTypeId = form.getValues('staffingTypeId');

                            if (candidateId && candidates.length > 0) {
                              const selectedCandidate = candidates.find(c => c.id.toString() === candidateId);
                              if (selectedCandidate && selectedCandidate.billingRate) {
                                const billingRate = parseFloat(selectedCandidate.billingRate.toString());
                                const attendanceDays = form.getValues('attendanceDays') || getWorkingDaysInMonth(new Date());

                                if (staffingTypeId && staffingTypes.length > 0) {
                                  const selectedStaffingType = staffingTypes.find(st => st.id.toString() === staffingTypeId);
                                  const staffingTypeName = selectedStaffingType?.name?.toLowerCase() || '';
                                  const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                                                         staffingTypeName.includes('contract') ||
                                                         staffingTypeName.includes('monthly');

                                  if (isMonthlyBilling) {
                                    form.setValue('rate', billingRate);
                                    // Use total working days in month for proper calculation
                                    const totalWorkingDays = getWorkingDaysInMonth(new Date());
                                    const calculatedBillingAmount = parseFloat(((billingRate / totalWorkingDays) * attendanceDays).toFixed(2));
                                    form.setValue('billingAmount', calculatedBillingAmount);
                                    toast.success("Rate regenerated successfully!");
                                  } else {
                                    form.setValue('rate', billingRate);
                                    form.setValue('billingAmount', billingRate);
                                    toast.success("Rate regenerated successfully!");
                                  }
                                } else {
                                  // Default to monthly billing
                                  form.setValue('rate', billingRate);
                                  // Use total working days in month for proper calculation
                                  const totalWorkingDays = getWorkingDaysInMonth(new Date());
                                  const calculatedBillingAmount = parseFloat(((billingRate / totalWorkingDays) * attendanceDays).toFixed(2));
                                  form.setValue('billingAmount', calculatedBillingAmount);
                                  toast.success("Rate regenerated successfully!");
                                }
                              } else {
                                toast.error("No billing rate found for selected candidate");
                              }
                            } else {
                              toast.error("Please select a candidate first");
                            }
                          }}
                          className="whitespace-nowrap"
                        >
                          Regenerate
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="billingAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Billing Amount *
                      <span className="ml-2 text-xs text-gray-500">
                        (Auto-calculated: Monthly rate for Full-time/Contract, Rate × Days for others)
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="0.00"
                        {...field}
                        className="bg-gray-50"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="attendanceDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Attendance Days *
                      <span className="ml-2 text-xs text-gray-500">
                        (Working days in month: {getWorkingDaysInMonth(new Date())})
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder={getWorkingDaysInMonth(new Date()).toString()}
                        {...field}
                        onChange={(e) => {
                          const newValue = parseInt(e.target.value) || getWorkingDaysInMonth(new Date());
                          console.log("=== ATTENDANCE CHANGED ===");
                          console.log("New attendance value:", newValue);

                          // Update the field first
                          field.onChange(newValue);

                          // Get current form values
                          const currentCandidateId = form.getValues("candidateId");
                          const currentStaffingTypeId = form.getValues("staffingTypeId");
                          const currentProjectId = form.getValues("projectId");

                          console.log("Current form values:", {
                            candidateId: currentCandidateId,
                            staffingTypeId: currentStaffingTypeId,
                            projectId: currentProjectId,
                            attendanceDays: newValue
                          });

                          // Immediate calculation without delay
                          if (currentCandidateId && currentStaffingTypeId && candidates?.length > 0 && staffingTypes?.length > 0) {
                            const selectedCandidate = candidates.find(c => c.id.toString() === currentCandidateId);
                            const selectedStaffingType = staffingTypes.find(st => st.id.toString() === currentStaffingTypeId);

                            console.log("Found candidate and staffing type:", {
                              candidate: selectedCandidate?.name,
                              candidateBillingRate: selectedCandidate?.billingRate,
                              staffingType: selectedStaffingType?.name
                            });

                            if (selectedCandidate && selectedStaffingType) {
                              // Use current rate from form, fallback to candidate's billing rate
                              const currentRate = parseFloat(form.getValues("rate")?.toString() || '0');
                              const candidateBillingRate = parseFloat(selectedCandidate.billingRate?.toString() || '0');
                              const rateToUse = currentRate > 0 ? currentRate : candidateBillingRate;

                              const staffingTypeName = selectedStaffingType.name?.toLowerCase() || '';

                              // Check if it's monthly billing - be more flexible with detection
                              const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                                                     staffingTypeName.includes('full time') ||
                                                     staffingTypeName.includes('fulltime') ||
                                                     staffingTypeName.includes('contract') ||
                                                     staffingTypeName.includes('monthly') ||
                                                     staffingTypeName === 'full time';

                              console.log("🔍 BILLING CALCULATION DEBUG:", {
                                currentRate,
                                candidateBillingRate,
                                rateToUse,
                                isMonthlyBilling,
                                staffingTypeName,
                                attendanceDays: newValue,
                                selectedStaffingType: selectedStaffingType?.name,
                                staffingTypeId: selectedStaffingType?.id
                              });

                              if (rateToUse > 0) {
                                let calculatedBillingAmount: number;

                                // Default to monthly billing if not clearly daily billing
                                const shouldUseMonthlyBilling = isMonthlyBilling || !staffingTypeName.includes('daily') && !staffingTypeName.includes('hourly');

                                if (shouldUseMonthlyBilling) {
                                  // For monthly billing: calculate proportional amount based on attendance
                                  // Formula: (monthly_rate / total_working_days_in_month) * attendance_days
                                  const totalWorkingDays = getWorkingDaysInMonth(new Date());
                                  calculatedBillingAmount = parseFloat(((rateToUse / totalWorkingDays) * newValue).toFixed(2));
                                  console.log(`📊 Monthly calculation: (${rateToUse} / ${totalWorkingDays}) * ${newValue} = ${calculatedBillingAmount}`);
                                } else {
                                  // Daily billing: rate * attendance days
                                  calculatedBillingAmount = parseFloat((rateToUse * newValue).toFixed(2));
                                  console.log(`📊 Daily calculation: ${rateToUse} * ${newValue} = ${calculatedBillingAmount}`);
                                }

                                console.log("✅ FINAL CALCULATED VALUES:", {
                                  monthlyRate: rateToUse,
                                  totalWorkingDays: shouldUseMonthlyBilling ? getWorkingDaysInMonth(new Date()) : 'N/A',
                                  attendanceDays: newValue,
                                  calculatedBillingAmount,
                                  billingMethod: shouldUseMonthlyBilling ? 'MONTHLY' : 'DAILY',
                                  calculation: shouldUseMonthlyBilling ?
                                    `(${rateToUse} / ${getWorkingDaysInMonth(new Date())}) * ${newValue} = ${calculatedBillingAmount}` :
                                    `${rateToUse} * ${newValue} = ${calculatedBillingAmount}`
                                });

                                // Only update billing amount, preserve the current rate
                                form.setValue("billingAmount", calculatedBillingAmount, { shouldValidate: true });

                                console.log("Updated billing amount based on attendance change:", calculatedBillingAmount);

                                // Calculate tax if project is selected
                                if (currentProjectId && projects?.length > 0) {
                                  const selectedProject = projects.find(p => p.id.toString() === currentProjectId);
                                  if (selectedProject) {
                                    const projectState = selectedProject.state?.toLowerCase().trim() || '';
                                    const billingAddress = selectedProject.billingAddress?.toLowerCase().trim() || '';

                                    const isMaharashtra = projectState.includes('maharashtra') ||
                                                        projectState.includes('mh') ||
                                                        projectState === 'maharashtra' ||
                                                        billingAddress.includes('maharashtra') ||
                                                        billingAddress.includes('pune') ||
                                                        billingAddress.includes('mumbai');

                                    let cgst = 0, sgst = 0, igst = 0, totalTax = 0;

                                    if (isMaharashtra) {
                                      cgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                                      sgst = parseFloat((calculatedBillingAmount * 0.09).toFixed(2));
                                      totalTax = cgst + sgst;
                                      igst = 0;
                                    } else {
                                      igst = parseFloat((calculatedBillingAmount * 0.18).toFixed(2));
                                      totalTax = igst;
                                      cgst = 0;
                                      sgst = 0;
                                    }

                                    const total = parseFloat((calculatedBillingAmount + totalTax).toFixed(2));

                                    console.log("Tax calculation:", {
                                      billingAmount: calculatedBillingAmount,
                                      isMaharashtra,
                                      cgst,
                                      sgst,
                                      igst,
                                      totalTax,
                                      total
                                    });

                                    // Update tax state variables
                                    setCgstAmount(cgst);
                                    setSgstAmount(sgst);
                                    setIgstAmount(igst);
                                    setIsIntraState(isMaharashtra);

                                    // Update form tax values
                                    form.setValue("taxAmount", totalTax, { shouldValidate: true });
                                    form.setValue("totalAmount", total, { shouldValidate: true });

                                    console.log("Updated tax values - Tax:", totalTax, "Total:", total);
                                  }
                                }

                                console.log("=== CALCULATION COMPLETED ===");
                              }
                            }
                          } else {
                            console.log("Missing required data for calculation");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Tax Amount Fields - Show different fields based on state */}
              {isIntraState ? (
                // Maharashtra: Show CGST and SGST separately
                <div className="grid grid-cols-2 gap-4">
                  <FormItem>
                    <FormLabel className="text-green-700">
                      CGST (9%)
                      <span className="ml-2 text-xs px-2 py-1 rounded bg-green-100 text-green-800">
                        Maharashtra
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="0.00"
                        value={cgstAmount.toFixed(2)}
                        readOnly
                        className="bg-green-50"
                      />
                    </FormControl>
                  </FormItem>

                  <FormItem>
                    <FormLabel className="text-green-700">
                      SGST (9%)
                      <span className="ml-2 text-xs px-2 py-1 rounded bg-green-100 text-green-800">
                        Maharashtra
                      </span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="0.00"
                        value={sgstAmount.toFixed(2)}
                        readOnly
                        className="bg-green-50"
                      />
                    </FormControl>
                  </FormItem>
                </div>
              ) : (
                // Other states: Show IGST
                <FormItem>
                  <FormLabel className="text-blue-700">
                    IGST (18%)
                    <span className="ml-2 text-xs px-2 py-1 rounded bg-blue-100 text-blue-800">
                      Inter-State
                    </span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="0.00"
                      value={igstAmount.toFixed(2)}
                      readOnly
                      className="bg-blue-50"
                    />
                  </FormControl>
                </FormItem>
              )}

              {/* Tax Summary */}
              <div className="p-4 bg-gray-50 rounded-lg border">
                <h4 className="font-medium text-gray-900 mb-2">Tax Summary</h4>
                <div className="space-y-1 text-sm">
                  {isIntraState ? (
                    <>
                      <div className="flex justify-between">
                        <span>CGST (9%):</span>
                        <span className="font-medium">₹{cgstAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>SGST (9%):</span>
                        <span className="font-medium">₹{sgstAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-1 font-medium">
                        <span>Total Tax:</span>
                        <span>₹{(cgstAmount + sgstAmount).toFixed(2)}</span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between">
                        <span>IGST (18%):</span>
                        <span className="font-medium">₹{igstAmount.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between border-t pt-1 font-medium">
                        <span>Total Tax:</span>
                        <span>₹{igstAmount.toFixed(2)}</span>
                      </div>
                    </>
                  )}
                </div>
              </div>

              {/* Hidden field for total tax amount (for form submission) */}
              <FormField
                control={form.control}
                name="taxAmount"
                render={({ field }) => (
                  <FormItem className="hidden">
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="totalAmount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Total Amount</FormLabel>
                    <FormControl>
                      <Input placeholder="0.00" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="hsnId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>HSN Code</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={hsnCodes.map(code => ({
                          value: code.id,
                          label: code.code,
                          description: code.description
                        }))}
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="Select HSN code"
                        error={!!form.formState.errors.hsnId}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="redberylAccountId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Redberyl Account</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        console.log("Redberyl account selected:", value);
                        field.onChange(value);
                      }}
                      value={field.value || undefined}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="hover:cursor-pointer">
                          <SelectValue placeholder="Select account" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[300px] z-[9999]">
                        {redberylAccounts.length === 0 ? (
                          <div className="px-2 py-4 text-center text-sm text-muted-foreground">
                            No accounts available
                          </div>
                        ) : (
                          redberylAccounts.map((account) => (
                            <SelectItem
                              key={account.id}
                              value={account.id.toString()}
                              title={`${account.bankName || ''} - ${account.accountNo || ''} - ${account.ifscCode || ''}`}
                              className="py-2"
                            >
                              <div className="flex flex-col">
                                <span className="font-medium">{account.name}</span>
                                <span className="text-xs text-muted-foreground">
                                  {account.bankName || 'Unknown Bank'} • {account.accountNo || 'No Account Number'}
                                </span>
                              </div>
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    {field.value && redberylAccounts.find(a => a.id.toString() === field.value) && (
                      <div className="mt-2 text-xs text-muted-foreground border p-2 rounded-md bg-muted/30">
                        <p className="mb-1">
                          <span className="font-medium">Bank:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.bankName || 'N/A'}
                        </p>
                        <p className="mb-1">
                          <span className="font-medium">Account No:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.accountNo || 'N/A'}
                        </p>
                        <p className="mb-1">
                          <span className="font-medium">IFSC:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.ifscCode || 'N/A'}
                        </p>
                        {redberylAccounts.find(a => a.id.toString() === field.value)?.gstn && (
                          <p className="mb-1">
                            <span className="font-medium">GSTN:</span> {redberylAccounts.find(a => a.id.toString() === field.value)?.gstn}
                          </p>
                        )}
                      </div>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="candidateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Candidate (Optional)</FormLabel>
                    <FormControl>
                      <CustomSelect
                        options={candidates.map(candidate => {
                          console.log("Mapping candidate for dropdown:", candidate);
                          return {
                            value: candidate.id.toString(),
                            label: candidate.name || "Unknown Candidate"
                          };
                        })}
                        value={field.value}
                        onChange={(value) => {
                          console.log("Candidate selected:", value);
                          field.onChange(value);

                          // Auto-generate invoice data based on selected candidate
                          const selected = candidates.find(c => c.id.toString() === value);
                          if (selected) {
                            console.log("Selected candidate details:", selected);

                            // Auto-fill client and project if available in candidate data
                            if (selected.clientId) {
                              console.log("Auto-filling client from candidate:", selected.clientId);
                              form.setValue('clientId', selected.clientId.toString());
                            }
                            if (selected.projectId) {
                              console.log("Auto-filling project from candidate:", selected.projectId);
                              form.setValue('projectId', selected.projectId.toString());
                            }

                            // If candidate has direct client/project objects, use those
                            if (selected.client && selected.client.id) {
                              console.log("Auto-filling client from candidate.client:", selected.client.id);
                              form.setValue('clientId', selected.client.id.toString());
                            }
                            if (selected.project && selected.project.id) {
                              console.log("Auto-filling project from candidate.project:", selected.project.id);
                              form.setValue('projectId', selected.project.id.toString());
                            }

                            // Auto-generate rate and billing amount from candidate's billing rate
                            if (selected.billingRate) {
                              const billingRate = parseFloat(selected.billingRate.toString());
                              const currentMonthWorkingDays = getWorkingDaysInMonth(new Date());

                              // Auto-fill attendance days with current month working days
                              form.setValue('attendanceDays', currentMonthWorkingDays);

                              // Auto-generate rate based on staffing type
                              const currentStaffingTypeId = form.getValues("staffingTypeId");
                              const currentAttendanceDays = currentMonthWorkingDays; // Use calculated working days

                              if (currentStaffingTypeId && staffingTypes && staffingTypes.length > 0) {
                                const selectedStaffingType = staffingTypes.find(st => st.id.toString() === currentStaffingTypeId);
                                const staffingTypeName = selectedStaffingType?.name?.toLowerCase() || '';

                                // Check if it's a monthly billing type
                                const isMonthlyBilling = staffingTypeName.includes('full-time') ||
                                                       staffingTypeName.includes('contract') ||
                                                       staffingTypeName.includes('monthly');

                                let calculatedRate = 0;
                                let calculatedBillingAmount = 0;

                                if (isMonthlyBilling) {
                                  // For monthly billing: rate = billing rate (monthly rate)
                                  calculatedRate = billingRate;
                                  // Billing amount = (monthly rate ÷ 30) × attendance days
                                  calculatedBillingAmount = parseFloat(((billingRate / 30) * currentAttendanceDays).toFixed(2));
                                } else {
                                  // For daily/hourly billing: rate = billing rate ÷ attendance days
                                  calculatedRate = currentAttendanceDays > 0 ? billingRate / currentAttendanceDays : billingRate;
                                  // Billing amount = rate × attendance days
                                  calculatedBillingAmount = parseFloat((calculatedRate * currentAttendanceDays).toFixed(2));
                                }

                                console.log("🎯 Auto-generating rate and billing amount:", {
                                  candidateName: selected.name,
                                  billingRate,
                                  staffingType: staffingTypeName,
                                  isMonthlyBilling,
                                  attendanceDays: currentAttendanceDays,
                                  workingDaysInMonth: currentMonthWorkingDays,
                                  calculatedRate,
                                  calculatedBillingAmount
                                });

                                form.setValue('rate', calculatedRate);
                                form.setValue('billingAmount', calculatedBillingAmount);
                              } else {
                                // If no staffing type selected, default to monthly rate
                                console.log("No staffing type selected, defaulting to monthly billing");
                                const calculatedBillingAmount = parseFloat(((billingRate / 30) * currentAttendanceDays).toFixed(2));
                                form.setValue('rate', billingRate);
                                form.setValue('billingAmount', calculatedBillingAmount);
                              }
                            }
                          }
                        }}
                        placeholder="Select candidate"
                        error={!!form.formState.errors.candidateId}
                      />
                    </FormControl>
                    <FormMessage />
                    {candidates.length === 0 && (
                      <p className="text-xs text-muted-foreground mt-1">
                        No candidates available. You can still create an invoice without a candidate.
                      </p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter invoice description here..."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="isRecurring"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Recurring Invoice</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Set this invoice to automatically recur
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="publishedToFinance"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Published to Finance</FormLabel>
                      <div className="text-sm text-muted-foreground">
                        Mark this invoice as published to finance department
                      </div>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                type="button"
                onClick={onCancel}
                className="bg-gradient-to-r from-gray-50 to-gray-100 hover:from-gray-100 hover:to-gray-200 text-gray-700 border-gray-300 hover:border-gray-400 shadow-sm hover:shadow-md transition-all duration-200"
              >
                Cancel
              </Button>
              <Button
                variant="outline"
                type="button"
                onClick={() => {
                  // Reset the form to initial values
                  if (isEditing && invoice) {
                    console.log("Resetting form to initial values");
                    setFormInitialized(false);

                    // Force a re-render
                    setTimeout(() => {
                      // Reset the form with the original invoice values
                      const billingAmount = parseFloat(invoice.amount.replace(/[₹$,]/g, '')) || 0;
                      const taxAmount = parseFloat(invoice.tax.replace(/[₹$,]/g, '')) || 0;
                      const totalAmount = parseFloat(invoice.total.replace(/[₹$,]/g, '')) || 0;

                      // Use pre-formatted invoice number if available, otherwise format it
                      const formattedInvoiceNumber = (invoice as any).formattedInvoiceNumber ||
                                                    formatInvoiceNumber((invoice as any).invoiceNumber || invoice.id, new Date(invoice.issueDate));

                      form.reset({
                        invoiceNumber: formattedInvoiceNumber,
                        billingAmount,
                        taxAmount,
                        totalAmount,
                        issueDate: new Date(invoice.issueDate),
                        dueDate: new Date(invoice.dueDate),
                        isRecurring: invoice.recurring,
                        publishedToFinance: invoice.publishedToFinance || false,
                        description: invoice.notes || "",
                        clientId: invoice.client || "",
                        projectId: invoice.project || "",
                        candidateId: invoice.candidate || "",
                        invoiceTypeId: invoice.invoiceType || "",
                        staffingTypeId: invoice.staffingType || "",
                        hsnId: invoice.hsnCode || "",
                        redberylAccountId: invoice.redberylAccount || "",
                        attendanceDays: 0,
                      });

                      setFormInitialized(true);
                    }, 100);

                    toast.info("Form has been reset to initial values");
                  } else {
                    form.reset();
                    toast.info("Form has been cleared");
                  }
                }}
                className="bg-gradient-to-r from-yellow-50 to-amber-50 hover:from-yellow-100 hover:to-amber-100 text-yellow-700 border-yellow-300 hover:border-yellow-400 shadow-sm hover:shadow-md transition-all duration-200"
              >
                Reset Form
              </Button>
              <Button
                type="submit"
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0"
                disabled={form.formState.isSubmitting}
              >
                {form.formState.isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditing ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  isEditing ? "Update Invoice" : "Create Invoice"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

// Wrap the InvoiceForm with the ErrorBoundary
const InvoiceFormWithErrorBoundary: React.FC<InvoiceFormProps> = (props) => {
  return (
    <ErrorBoundary>
      <InvoiceForm {...props} />
    </ErrorBoundary>
  );
};

export default InvoiceFormWithErrorBoundary;
