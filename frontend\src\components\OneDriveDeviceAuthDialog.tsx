import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Copy, ExternalLink, CheckCircle, Clock, AlertCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface DeviceCodeData {
  device_code: string;
  user_code: string;
  verification_uri: string;
  expires_in: number;
  interval: number;
  message?: string;
}

interface OneDriveDeviceAuthDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (accessToken: string) => void;
  onError: (error: string) => void;
  deviceCodeData: DeviceCodeData | null;
}

const OneDriveDeviceAuthDialog: React.FC<OneDriveDeviceAuthDialogProps> = ({
  isOpen,
  onClose,
  onSuccess,
  onError,
  deviceCodeData
}) => {
  const [status, setStatus] = useState<'waiting' | 'polling' | 'success' | 'error'>('waiting');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [pollCount, setPollCount] = useState(0);

  useEffect(() => {
    if (deviceCodeData) {
      setTimeRemaining(deviceCodeData.expires_in);
      setStatus('waiting');
    }
  }, [deviceCodeData]);

  useEffect(() => {
    if (timeRemaining > 0 && status !== 'success') {
      const timer = setTimeout(() => {
        setTimeRemaining(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (timeRemaining === 0 && status === 'polling') {
      setStatus('error');
      onError('Device code expired. Please try again.');
    }
  }, [timeRemaining, status, onError]);

  const copyCode = async () => {
    if (deviceCodeData?.user_code) {
      try {
        await navigator.clipboard.writeText(deviceCodeData.user_code);
        toast.success('Code copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy code');
      }
    }
  };

  const openVerificationPage = () => {
    if (deviceCodeData?.verification_uri) {
      window.open(deviceCodeData.verification_uri, '_blank');
      setStatus('polling');
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'waiting':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'polling':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'waiting':
        return 'Ready to authenticate';
      case 'polling':
        return `Waiting for authentication... (${pollCount} checks)`;
      case 'success':
        return 'Authentication successful!';
      case 'error':
        return 'Authentication failed';
      default:
        return 'Initializing...';
    }
  };

  if (!deviceCodeData) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getStatusIcon()}
            OneDrive Authentication
          </DialogTitle>
          <DialogDescription>
            Complete the authentication process to save invoices to OneDrive
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Status:</span>
            <Badge variant={status === 'success' ? 'default' : status === 'error' ? 'destructive' : 'secondary'}>
              {getStatusText()}
            </Badge>
          </div>

          {/* Time Remaining */}
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Time remaining:</span>
            <span className="text-sm font-mono">{formatTime(timeRemaining)}</span>
          </div>

          {/* Instructions Card */}
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="text-center">
                  <h4 className="font-semibold mb-2">Authentication Code</h4>
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="text-2xl font-mono font-bold text-blue-900 mb-2">
                      {deviceCodeData.user_code}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={copyCode}
                      className="w-full"
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Copy Code
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <h5 className="font-medium">Steps:</h5>
                  <ol className="text-sm space-y-1 list-decimal list-inside text-gray-600">
                    <li>Copy the code above (or click Copy Code)</li>
                    <li>Click "Open Microsoft Page" below</li>
                    <li>Paste the code and sign in with your Microsoft account</li>
                    <li>Return to this page - it will update automatically</li>
                  </ol>
                </div>

                <Button
                  onClick={openVerificationPage}
                  className="w-full"
                  disabled={status === 'success' || status === 'error'}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open Microsoft Page
                </Button>

                {status === 'polling' && (
                  <div className="text-center text-sm text-gray-600">
                    <Loader2 className="h-4 w-4 animate-spin mx-auto mb-1" />
                    Waiting for you to complete authentication...
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {status === 'error' && (
              <Button onClick={() => window.location.reload()}>
                Try Again
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OneDriveDeviceAuthDialog;
