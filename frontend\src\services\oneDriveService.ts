import { apiRequest } from '@/utils/api';

export interface OneDriveUploadResponse {
  success: boolean;
  message: string;
  fileId?: string;
  fileName?: string;
  webUrl?: string;
  downloadUrl?: string;
  fileSize?: number;
  uploadedAt?: string;
  error?: string;
}

export interface OneDriveAuthResponse {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface OneDriveAuthStatus {
  authenticated: boolean;
  success: boolean;
  error?: string;
}

class OneDriveService {
  private accessToken: string | null = null;

  /**
   * Get OneDrive authorization URL
   */
  async getAuthorizationUrl(): Promise<OneDriveAuthResponse> {
    try {
      const response = await apiRequest('/onedrive/auth-url', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error getting OneDrive authorization URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get authorization URL'
      };
    }
  }

  /**
   * Authenticate with OneDrive using device code flow (no redirect URI needed)
   */
  async authenticateWithDeviceCode(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      // Start device code flow
      const deviceCodeResponse = await apiRequest('/onedrive/device-code', {
        method: 'POST'
      });

      if (!deviceCodeResponse.success) {
        return {
          success: false,
          error: deviceCodeResponse.error || 'Failed to start device code flow'
        };
      }

      const { device_code, user_code, verification_uri, interval = 5 } = deviceCodeResponse;

      // Show instructions to user
      const proceed = window.confirm(
        `OneDrive Authentication Required\n\n` +
        `1. Copy this code: ${user_code}\n` +
        `2. Click OK to open the verification page\n` +
        `3. Enter the code and sign in with your Microsoft account\n\n` +
        `Code: ${user_code}`
      );

      if (!proceed) {
        return {
          success: false,
          error: 'Authentication cancelled by user'
        };
      }

      // Copy code to clipboard
      try {
        await navigator.clipboard.writeText(user_code);
      } catch (e) {
        // Clipboard API might not be available
      }

      // Open verification page
      window.open(verification_uri, '_blank');

      // Poll for token
      return new Promise((resolve) => {
        const pollInterval = setInterval(async () => {
          try {
            const tokenResponse = await apiRequest('/onedrive/device-token', {
              method: 'POST',
              body: JSON.stringify({ device_code })
            });

            if (tokenResponse.success && tokenResponse.access_token) {
              clearInterval(pollInterval);
              this.accessToken = tokenResponse.access_token;
              localStorage.setItem('onedrive_access_token', tokenResponse.access_token);
              resolve({
                success: true,
                accessToken: tokenResponse.access_token
              });
            } else if (tokenResponse.error === 'authorization_declined') {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: 'Authorization was declined'
              });
            } else if (tokenResponse.error === 'expired_token') {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: 'Device code expired. Please try again.'
              });
            } else if (tokenResponse.error !== 'authorization_pending') {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: tokenResponse.error || 'Authentication failed'
              });
            }
            // Continue polling if authorization_pending
          } catch (error) {
            clearInterval(pollInterval);
            resolve({
              success: false,
              error: error instanceof Error ? error.message : 'Authentication failed'
            });
          }
        }, interval * 1000);

        // Timeout after 15 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          resolve({
            success: false,
            error: 'Authentication timeout. Please try again.'
          });
        }, 15 * 60 * 1000);
      });
    } catch (error) {
      console.error('Error during device code authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using popup window (original method)
   */
  async authenticate(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      const authResponse = await this.getAuthorizationUrl();
      
      if (!authResponse.success || !authResponse.authUrl) {
        return {
          success: false,
          error: authResponse.error || 'Failed to get authorization URL'
        };
      }

      return new Promise((resolve) => {
        const popup = window.open(
          authResponse.authUrl,
          'onedrive-auth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        if (!popup) {
          resolve({
            success: false,
            error: 'Failed to open popup window. Please allow popups for this site.'
          });
          return;
        }

        // Listen for messages from the popup
        const messageListener = (event: MessageEvent) => {
          if (event.origin !== window.location.origin) {
            return;
          }

          if (event.data.success) {
            this.accessToken = event.data.accessToken;
            localStorage.setItem('onedrive_access_token', event.data.accessToken);
            window.removeEventListener('message', messageListener);
            resolve({
              success: true,
              accessToken: event.data.accessToken
            });
          } else {
            window.removeEventListener('message', messageListener);
            resolve({
              success: false,
              error: event.data.error || 'Authentication failed'
            });
          }
        };

        window.addEventListener('message', messageListener);

        // Check if popup was closed manually
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', messageListener);
            resolve({
              success: false,
              error: 'Authentication was cancelled'
            });
          }
        }, 1000);
      });
    } catch (error) {
      console.error('Error during OneDrive authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  async checkAuthentication(): Promise<OneDriveAuthStatus> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return { authenticated: false, success: true };
      }

      const response = await apiRequest('/onedrive/check-auth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.authenticated) {
        this.accessToken = token;
      } else {
        this.accessToken = null;
        localStorage.removeItem('onedrive_access_token');
      }

      return response;
    } catch (error) {
      console.error('Error checking OneDrive authentication:', error);
      this.accessToken = null;
      localStorage.removeItem('onedrive_access_token');
      return {
        authenticated: false,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check authentication'
      };
    }
  }

  /**
   * Upload PDF to OneDrive
   */
  async uploadPdf(pdfBlob: Blob, invoiceNumber?: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const formData = new FormData();
      formData.append('file', pdfBlob, `invoice_${invoiceNumber || 'document'}.pdf`);
      
      if (invoiceNumber) {
        formData.append('invoiceNumber', invoiceNumber);
      }

      const response = await fetch('/api/onedrive/upload-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload invoice PDF with base64 content
   */
  async uploadInvoicePdf(pdfBase64: string, invoiceNumber: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const response = await apiRequest('/onedrive/upload-invoice-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pdfContent: pdfBase64,
          invoiceNumber: invoiceNumber
        })
      });

      return response;
    } catch (error) {
      console.error('Error uploading invoice PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload invoice PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return this.accessToken || localStorage.getItem('onedrive_access_token');
  }

  /**
   * Clear authentication
   */
  clearAuthentication(): void {
    this.accessToken = null;
    localStorage.removeItem('onedrive_access_token');
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return !!(this.accessToken || localStorage.getItem('onedrive_access_token'));
  }
}

export const oneDriveService = new OneDriveService();
export default oneDriveService;
