import { apiRequest } from '@/utils/api';

export interface OneDriveUploadResponse {
  success: boolean;
  message: string;
  fileId?: string;
  fileName?: string;
  webUrl?: string;
  downloadUrl?: string;
  fileSize?: number;
  uploadedAt?: string;
  error?: string;
}

export interface OneDriveAuthResponse {
  success: boolean;
  authUrl?: string;
  error?: string;
}

export interface OneDriveAuthStatus {
  authenticated: boolean;
  success: boolean;
  error?: string;
}

class OneDriveService {
  private accessToken: string | null = null;

  /**
   * Get OneDrive authorization URL
   */
  async getAuthorizationUrl(): Promise<OneDriveAuthResponse> {
    try {
      const response = await apiRequest('/onedrive/auth-url', {
        method: 'GET',
      });
      return response;
    } catch (error) {
      console.error('Error getting OneDrive authorization URL:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get authorization URL'
      };
    }
  }

  /**
   * Authenticate with OneDrive using device code flow (no redirect URI needed)
   * This method is more reliable when popup authentication fails
   */
  async authenticateWithDeviceCode(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      // Start device code flow
      const deviceCodeResponse = await apiRequest('/onedrive/device-code', {
        method: 'POST'
      });

      if (!deviceCodeResponse.success) {
        return {
          success: false,
          error: deviceCodeResponse.error || 'Failed to start device code flow'
        };
      }

      const { device_code, user_code, verification_uri, interval = 5 } = deviceCodeResponse;

      // Copy code to clipboard first
      try {
        await navigator.clipboard.writeText(user_code);
      } catch (e) {
        console.log('Clipboard not available, user will need to copy manually');
      }

      // Show detailed instructions to user
      const proceed = window.confirm(
        `OneDrive Device Code Authentication\n\n` +
        `STEP 1: Copy this code (already copied to clipboard):\n` +
        `${user_code}\n\n` +
        `STEP 2: Click OK to open Microsoft verification page\n\n` +
        `STEP 3: Paste the code and sign in\n\n` +
        `STEP 4: Return to this page - authentication will complete automatically\n\n` +
        `Code: ${user_code}\n\n` +
        `Click OK to continue or Cancel to abort.`
      );

      if (!proceed) {
        return {
          success: false,
          error: 'Authentication cancelled by user'
        };
      }

      // Open verification page
      window.open(verification_uri, '_blank');

      // Show a toast with instructions
      if (typeof window !== 'undefined' && window.navigator) {
        // Import toast dynamically to avoid SSR issues
        try {
          const { toast } = await import('sonner');
          toast.info('Authentication in Progress', {
            description: `Enter code ${user_code} on the Microsoft page. This window will update automatically.`,
            duration: 10000
          });
        } catch (e) {
          console.log('Toast not available');
        }
      }

      // Poll for token with better feedback
      return new Promise((resolve) => {
        let pollCount = 0;
        const maxPolls = Math.floor((15 * 60) / interval); // 15 minutes worth of polls

        const pollInterval = setInterval(async () => {
          try {
            pollCount++;
            console.log(`Polling for device token (attempt ${pollCount}/${maxPolls})...`);

            const tokenResponse = await apiRequest('/onedrive/device-token', {
              method: 'POST',
              body: JSON.stringify({ device_code })
            });

            console.log('Device token response:', tokenResponse);

            if (tokenResponse.success && tokenResponse.access_token) {
              clearInterval(pollInterval);
              this.accessToken = tokenResponse.access_token;
              localStorage.setItem('onedrive_access_token', tokenResponse.access_token);

              // Show success message
              try {
                const { toast } = await import('sonner');
                toast.success('OneDrive Authentication Successful!', {
                  description: 'You can now save invoices to OneDrive.'
                });
              } catch (e) {
                console.log('Toast not available');
              }

              resolve({
                success: true,
                accessToken: tokenResponse.access_token
              });
            } else if (tokenResponse.error === 'authorization_declined') {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: 'Authorization was declined by user'
              });
            } else if (tokenResponse.error === 'expired_token') {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: 'Device code expired. Please try again.'
              });
            } else if (tokenResponse.error === 'authorization_pending') {
              // This is normal - continue polling
              console.log('Still waiting for user to complete authentication...');

              // Show periodic reminders
              if (pollCount % 6 === 0) { // Every 30 seconds (assuming 5 second interval)
                try {
                  const { toast } = await import('sonner');
                  toast.info('Still waiting for authentication...', {
                    description: `Please complete the sign-in process on the Microsoft page. Code: ${user_code}`,
                    duration: 5000
                  });
                } catch (e) {
                  console.log('Toast not available');
                }
              }
            } else {
              clearInterval(pollInterval);
              resolve({
                success: false,
                error: tokenResponse.error || 'Authentication failed'
              });
            }
          } catch (error) {
            console.error('Error during device token polling:', error);
            clearInterval(pollInterval);
            resolve({
              success: false,
              error: error instanceof Error ? error.message : 'Authentication failed'
            });
          }
        }, interval * 1000);

        // Timeout after 15 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          resolve({
            success: false,
            error: 'Authentication timeout. Please try again.'
          });
        }, 15 * 60 * 1000);
      });
    } catch (error) {
      console.error('Error during device code authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using popup window (original method)
   */
  async authenticate(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      // First try popup authentication
      const popupResult = await this.authenticateWithPopup();

      if (popupResult.success) {
        return popupResult;
      }

      // If popup fails, fallback to device code flow
      console.log('Popup authentication failed, trying device code flow:', popupResult.error);

      // Show user a choice
      const useDeviceCode = window.confirm(
        `Popup authentication failed: ${popupResult.error}\n\n` +
        `Would you like to try an alternative authentication method?\n\n` +
        `Click OK to use device code authentication, or Cancel to retry popup.`
      );

      if (useDeviceCode) {
        return await this.authenticateWithDeviceCode();
      } else {
        return popupResult;
      }
    } catch (error) {
      console.error('Error during OneDrive authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using popup window
   */
  private async authenticateWithPopup(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      const authResponse = await this.getAuthorizationUrl();

      if (!authResponse.success || !authResponse.authUrl) {
        return {
          success: false,
          error: authResponse.error || 'Failed to get authorization URL'
        };
      }

      return new Promise((resolve) => {
        const popup = window.open(
          authResponse.authUrl,
          'onedrive-auth',
          'width=600,height=700,scrollbars=yes,resizable=yes'
        );

        if (!popup) {
          resolve({
            success: false,
            error: 'Failed to open popup window. Please allow popups for this site.'
          });
          return;
        }

        let resolved = false;

        // Listen for messages from the popup
        const messageListener = (event: MessageEvent) => {
          // Allow messages from Microsoft domains and localhost
          const allowedOrigins = [
            window.location.origin,
            'https://login.microsoftonline.com',
            'https://login.live.com'
          ];

          if (!allowedOrigins.includes(event.origin)) {
            return;
          }

          if (resolved) return;

          if (event.data && typeof event.data === 'object') {
            if (event.data.success && event.data.accessToken) {
              resolved = true;
              this.accessToken = event.data.accessToken;
              localStorage.setItem('onedrive_access_token', event.data.accessToken);
              window.removeEventListener('message', messageListener);
              clearInterval(checkClosed);
              resolve({
                success: true,
                accessToken: event.data.accessToken
              });
            } else if (event.data.error) {
              resolved = true;
              window.removeEventListener('message', messageListener);
              clearInterval(checkClosed);
              resolve({
                success: false,
                error: event.data.error || 'Authentication failed'
              });
            }
          }
        };

        window.addEventListener('message', messageListener);

        // Check if popup was closed manually or if URL contains callback
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            if (!resolved) {
              resolved = true;
              clearInterval(checkClosed);
              window.removeEventListener('message', messageListener);
              resolve({
                success: false,
                error: 'Authentication window was closed. Please try again.'
              });
            }
            return;
          }

          // Check if popup URL contains callback parameters
          try {
            const popupUrl = popup.location.href;
            if (popupUrl && popupUrl.includes('/api/onedrive/callback')) {
              // Let the callback handle the response
              return;
            }
          } catch (e) {
            // Cross-origin error is expected during auth flow
          }
        }, 1000);

        // Timeout after 5 minutes
        setTimeout(() => {
          if (!resolved) {
            resolved = true;
            clearInterval(checkClosed);
            window.removeEventListener('message', messageListener);
            if (!popup.closed) {
              popup.close();
            }
            resolve({
              success: false,
              error: 'Authentication timeout. Please try again.'
            });
          }
        }, 5 * 60 * 1000);
      });
    } catch (error) {
      console.error('Error during popup authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Popup authentication failed'
      };
    }
  }

  /**
   * Authenticate with OneDrive using direct redirect (no popup)
   */
  async authenticateWithRedirect(): Promise<{ success: boolean; authUrl?: string; error?: string }> {
    try {
      const authResponse = await this.getAuthorizationUrl();

      if (!authResponse.success || !authResponse.authUrl) {
        return {
          success: false,
          error: authResponse.error || 'Failed to get authorization URL'
        };
      }

      // Store current page URL to return to after auth
      localStorage.setItem('onedrive_return_url', window.location.href);

      // Redirect to Microsoft auth
      window.location.href = authResponse.authUrl;

      return {
        success: true,
        authUrl: authResponse.authUrl
      };
    } catch (error) {
      console.error('Error during redirect authentication:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Redirect authentication failed'
      };
    }
  }

  /**
   * Handle authentication callback from redirect
   */
  async handleAuthCallback(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    try {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const error = urlParams.get('error');
      const state = urlParams.get('state');

      if (error) {
        return {
          success: false,
          error: `Authentication error: ${error}`
        };
      }

      if (!code) {
        return {
          success: false,
          error: 'No authorization code received'
        };
      }

      // Exchange code for token
      const response = await apiRequest('/onedrive/exchange-code', {
        method: 'POST',
        body: JSON.stringify({ code, state })
      });

      if (response.success && response.accessToken) {
        this.accessToken = response.accessToken;
        localStorage.setItem('onedrive_access_token', response.accessToken);

        // Clean up URL parameters
        const cleanUrl = window.location.origin + window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);

        return {
          success: true,
          accessToken: response.accessToken
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to exchange authorization code'
        };
      }
    } catch (error) {
      console.error('Error handling auth callback:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to handle authentication callback'
      };
    }
  }

  /**
   * Check if user is authenticated
   */
  async checkAuthentication(): Promise<OneDriveAuthStatus> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return { authenticated: false, success: true };
      }

      const response = await apiRequest('/onedrive/check-auth', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.authenticated) {
        this.accessToken = token;
      } else {
        this.accessToken = null;
        localStorage.removeItem('onedrive_access_token');
      }

      return response;
    } catch (error) {
      console.error('Error checking OneDrive authentication:', error);
      this.accessToken = null;
      localStorage.removeItem('onedrive_access_token');
      return {
        authenticated: false,
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check authentication'
      };
    }
  }

  /**
   * Upload PDF to OneDrive
   */
  async uploadPdf(pdfBlob: Blob, invoiceNumber?: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const formData = new FormData();
      formData.append('file', pdfBlob, `invoice_${invoiceNumber || 'document'}.pdf`);
      
      if (invoiceNumber) {
        formData.append('invoiceNumber', invoiceNumber);
      }

      const response = await fetch('/api/onedrive/upload-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error uploading PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload invoice PDF with base64 content
   */
  async uploadInvoicePdf(pdfBase64: string, invoiceNumber: string): Promise<OneDriveUploadResponse> {
    try {
      const token = this.accessToken || localStorage.getItem('onedrive_access_token');
      
      if (!token) {
        return {
          success: false,
          message: 'Not authenticated with OneDrive',
          error: 'Please authenticate with OneDrive first'
        };
      }

      const response = await apiRequest('/onedrive/upload-invoice-pdf', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pdfContent: pdfBase64,
          invoiceNumber: invoiceNumber
        })
      });

      return response;
    } catch (error) {
      console.error('Error uploading invoice PDF to OneDrive:', error);
      return {
        success: false,
        message: 'Failed to upload invoice PDF to OneDrive',
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return this.accessToken || localStorage.getItem('onedrive_access_token');
  }

  /**
   * Clear authentication
   */
  clearAuthentication(): void {
    this.accessToken = null;
    localStorage.removeItem('onedrive_access_token');
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return !!(this.accessToken || localStorage.getItem('onedrive_access_token'));
  }
}

export const oneDriveService = new OneDriveService();
export default oneDriveService;
