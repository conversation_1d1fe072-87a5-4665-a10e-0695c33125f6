import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";

// Define the form schema
const bdmFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters."),
  email: z.string().email("Invalid email address.").optional().or(z.literal("")),
  phone: z.string().optional().or(z.literal("")),
  commissionRate: z.string()
    .refine(
      (val) => !val || !isNaN(parseFloat(val)),
      { message: "Commission rate must be a valid number" }
    )
    .refine(
      (val) => !val || (parseFloat(val) >= 0 && parseFloat(val) <= 100),
      { message: "Commission rate must be between 0 and 100" }
    )
    .optional()
    .or(z.literal("")),
  billingAddress: z.string().optional().or(z.literal("")),
  gstNumber: z.string().optional().or(z.literal("")),
  notes: z.string().optional().or(z.literal("")),
});

// Define the form values type
type BdmFormValues = z.infer<typeof bdmFormSchema>;

// Define the processed data type that will be sent to the API
interface ProcessedBdmData {
  name: string;
  email: string | null;
  phone: string | null;
  commissionRate: number;
  billingAddress: string | null;
  gstNumber: string | null;
  notes: string | null;
}

// Define the props for the component
interface BdmFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  bdm?: any; // The BDM to edit, if any
  onSave?: (data: ProcessedBdmData) => Promise<any> | any;
}

export function BdmFormDialog({
  open,
  onOpenChange,
  bdm,
  onSave,
}: BdmFormDialogProps) {
  // Add a local state for tracking submission state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize the form
  const form = useForm<BdmFormValues>({
    resolver: zodResolver(bdmFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      commissionRate: "",
      billingAddress: "",
      gstNumber: "",
      notes: "",
    },
  });

  // Reset the form when the dialog opens or the BDM changes
  useEffect(() => {
    if (open) {
      // Reset submission state
      setIsSubmitting(false);

      if (bdm) {
        form.reset({
          name: bdm.name || "",
          email: bdm.email || "",
          phone: bdm.phone || "",
          commissionRate: bdm.commissionRate ? String(bdm.commissionRate) : "",
          billingAddress: bdm.billingAddress || "",
          gstNumber: bdm.gstNumber || "",
          notes: bdm.notes || "",
        });
      } else {
        form.reset({
          name: "",
          email: "",
          phone: "",
          commissionRate: "",
          billingAddress: "",
          gstNumber: "",
          notes: "",
        });
      }
    }
  }, [open, bdm, form, setIsSubmitting]);

  // Handle form submission
  const onSubmit = async (data: BdmFormValues) => {
    // If already submitting, prevent multiple submissions
    if (isSubmitting) return;

    console.log("Form data before processing:", data);

    try {
      // Set submitting state to true
      setIsSubmitting(true);

      // Process the data before saving - format it for the API
      const processedData: ProcessedBdmData = {
        name: data.name,
        email: data.email || null,
        phone: data.phone || null,
        commissionRate: data.commissionRate && data.commissionRate.toString().trim() !== ""
          ? parseFloat(data.commissionRate.toString())
          : 0,
        billingAddress: data.billingAddress || null,
        gstNumber: data.gstNumber || null,
        notes: data.notes || null,
      };

      // Validate commission rate is a valid number
      if (isNaN(processedData.commissionRate)) {
        toast.error("Commission rate must be a valid number");
        setIsSubmitting(false);
        return;
      }

      // Ensure commission rate is a valid number between 0 and 100
      if (processedData.commissionRate < 0 || processedData.commissionRate > 100) {
        toast.error("Commission rate must be between 0 and 100");
        setIsSubmitting(false);
        return;
      }

      // Validate GST Number format if provided
      if (processedData.gstNumber && typeof processedData.gstNumber === 'string') {
        // Remove any spaces or special characters
        processedData.gstNumber = processedData.gstNumber.replace(/[^a-zA-Z0-9]/g, '');

        // Check if it's a valid format (basic check)
        if (processedData.gstNumber.length < 10 || processedData.gstNumber.length > 15) {
          // Don't error out, just log a warning - some GST numbers might be shorter
          console.warn("GST Number has unusual length:", processedData.gstNumber.length);
        }
      }

      console.log("Processed data for saving:", processedData);

      if (onSave) {
        try {
          // Log the attempt
          console.log(`Attempting to ${bdm ? "update" : "create"} BDM with data:`, processedData);

          // Show a loading toast
          const loadingToast = toast.loading(`${bdm ? "Updating" : "Creating"} BDM...`);

          try {
            // Call the save function with a timeout
            console.log("Calling onSave with processed data:", processedData);

            // Create a promise that will reject after 15 seconds
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error("Request timed out after 15 seconds")), 15000);
            });

            // Race the save function against the timeout
            const result = await Promise.race([
              onSave(processedData),
              timeoutPromise
            ]);

            console.log("Save result:", result);

            // Dismiss the loading toast
            toast.dismiss(loadingToast);

            // Don't show success message here - let parent handle it
            // Don't close dialog here - let parent handle it after refetch
            // No need to reload the page, the parent component will handle refreshing the data
          } catch (apiError: any) {
            // Dismiss the loading toast
            toast.dismiss(loadingToast);

            console.error('API Error saving BDM:', apiError);

            // Show a more detailed error message
            let errorMessage = `Failed to ${bdm ? "update" : "create"} BDM`;

            // Check if the error is from our ApiResponseDto format
            if (apiError.apiResponse && apiError.apiResponse.message) {
              errorMessage += `: ${apiError.apiResponse.message}`;
            } else if (apiError.message) {
              errorMessage += `: ${apiError.message}`;
            }

            if (apiError.status) {
              errorMessage += ` (Status: ${apiError.status})`;
            }

            toast.error(errorMessage);

            // Log additional details for debugging
            console.error('API Error Details:', {
              message: apiError.message,
              status: apiError.status,
              statusText: apiError.statusText,
              url: apiError.url,
              apiResponse: apiError.apiResponse
            });

            // Try a direct fetch as a last resort
            try {
              console.log('Trying direct fetch to create BDM...');
              const authHeader = 'Basic ' + btoa('admin:admin123');

              const directResponse = await fetch('/bdms', {
                method: 'POST',
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json',
                  'Authorization': authHeader
                },
                body: JSON.stringify(processedData),
                credentials: 'include'
              });

              if (directResponse.ok) {
                const data = await directResponse.json();
                console.log('Successfully created BDM with direct fetch:', data);

                toast.success(`BDM created successfully with direct fetch!`);

                // Close the dialog
                onOpenChange(false);

                // No need to reload the page, the parent component will handle refreshing the data

                return;
              } else {
                console.error('Direct fetch failed:', directResponse.status, directResponse.statusText);
              }
            } catch (directError) {
              console.error('Error with direct fetch for BDM creation:', directError);
            }
          }
        } catch (error) {
          console.error("Error in form submission:", error);
          toast.error(`Failed to ${bdm ? "update" : "create"} BDM. Please try again.`);
        } finally {
          // Reset the submitting state
          setIsSubmitting(false);
        }
      }
    } catch (error) {
      console.error("Error processing form data:", error);
      toast.error(`Failed to process form data. Please check your inputs and try again.`);
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{bdm ? "Edit BDM" : "Add New BDM"}</DialogTitle>
          <DialogDescription>
            Fill in the BDM details below to {bdm ? "update" : "add"} them to your system.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>BDM Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone</FormLabel>
                      <FormControl>
                        <Input placeholder="****** 567 8900" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="commissionRate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commission Rate (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          max="100"
                          placeholder="5.00"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="gstNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>GST Number (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="22AAAAA0000A1Z5" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={form.control}
                name="billingAddress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Billing Address</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="123 Business Ave, Suite 100, City, ST 12345"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Additional notes about the BDM..."
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter className="w-full">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="w-full sm:w-auto"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="w-full sm:w-auto"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="mr-2">
                      <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    </span>
                    {bdm ? "Updating..." : "Adding..."}
                  </>
                ) : (
                  bdm ? "Update BDM" : "Add BDM"
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
