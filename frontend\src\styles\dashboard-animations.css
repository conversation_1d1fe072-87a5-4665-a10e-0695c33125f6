/* Dashboard Fancy Animations */

/* Floating animation for stat cards */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

/* Fade in animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale in animation */
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Pulse glow animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.4);
  }
}

/* Gradient shift animation */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Number counter animation */
@keyframes counter-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icon bounce animation */
@keyframes icon-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

/* Shimmer loading animation */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Utility classes for dashboard */
.dashboard-card-float {
  animation: float 4s ease-in-out infinite;
}

.dashboard-card-float:nth-child(1) {
  animation-delay: 0s;
}

.dashboard-card-float:nth-child(2) {
  animation-delay: 0.5s;
}

.dashboard-card-float:nth-child(3) {
  animation-delay: 1s;
}

.dashboard-card-float:nth-child(4) {
  animation-delay: 1.5s;
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out;
}

.dashboard-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.dashboard-gradient-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient-shift 15s ease infinite;
}

.dashboard-counter {
  animation: counter-up 0.6s ease-out;
}

.dashboard-icon-bounce:hover {
  animation: icon-bounce 0.6s ease-in-out;
}

.dashboard-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Enhanced hover effects */
.dashboard-card-enhanced {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.dashboard-card-enhanced:hover::before {
  left: 100%;
}

/* Staggered animation for multiple cards */
.dashboard-grid > * {
  animation: counter-up 0.6s ease-out;
}

.dashboard-grid > *:nth-child(1) { animation-delay: 0.1s; }
.dashboard-grid > *:nth-child(2) { animation-delay: 0.2s; }
.dashboard-grid > *:nth-child(3) { animation-delay: 0.3s; }
.dashboard-grid > *:nth-child(4) { animation-delay: 0.4s; }

/* Responsive enhancements */
@media (max-width: 768px) {
  .dashboard-card-float {
    animation: none; /* Disable float on mobile for better performance */
  }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
  .dashboard-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
  
  .dashboard-shimmer {
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
  }
}

/* Loading skeleton animation */
.dashboard-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Trend indicator animations */
.trend-up {
  animation: icon-bounce 0.6s ease-in-out;
  color: #10b981;
}

.trend-down {
  animation: icon-bounce 0.6s ease-in-out;
  color: #ef4444;
}

/* Micro-interactions */
.dashboard-interactive {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard-interactive:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-interactive:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
