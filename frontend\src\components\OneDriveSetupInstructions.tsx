import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ExternalLink, Co<PERSON>, CheckCircle, AlertTriangle } from 'lucide-react';
import { toast } from 'sonner';

interface OneDriveSetupInstructionsProps {
  className?: string;
}

const OneDriveSetupInstructions: React.FC<OneDriveSetupInstructionsProps> = ({ className = '' }) => {
  const redirectUri = 'http://localhost:8091/api/onedrive/callback';
  const clientId = '86756722-ad2a-4ac0-8806-e2705653949a';
  const azurePortalUrl = 'https://portal.azure.com/#view/Microsoft_AAD_RegisteredApps/ApplicationsListBlade';

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success(`${label} copied to clipboard!`);
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          OneDrive Setup Instructions
        </CardTitle>
        <CardDescription>
          Follow these steps to fix OneDrive authentication issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Problem Description */}
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Issue:</strong> OneDrive authentication fails because the redirect URI is not registered in Azure Portal.
            This is required for OAuth security.
          </AlertDescription>
        </Alert>

        {/* Step-by-step instructions */}
        <div className="space-y-4">
          <h4 className="font-semibold text-lg">Fix Steps:</h4>
          
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
              <div className="flex-1">
                <p className="font-medium">Open Azure Portal</p>
                <p className="text-sm text-gray-600 mb-2">Go to the Azure Portal and navigate to App registrations</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(azurePortalUrl, '_blank')}
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Open Azure Portal
                </Button>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
              <div className="flex-1">
                <p className="font-medium">Find Your App</p>
                <p className="text-sm text-gray-600 mb-2">Search for your app using this Client ID:</p>
                <div className="flex items-center gap-2">
                  <code className="bg-gray-100 px-2 py-1 rounded text-sm">{clientId}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(clientId, 'Client ID')}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
              <div className="flex-1">
                <p className="font-medium">Go to Authentication</p>
                <p className="text-sm text-gray-600">Click on your app, then navigate to the "Authentication" section in the left sidebar</p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border border-green-200">
              <div className="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</div>
              <div className="flex-1">
                <p className="font-medium">Add Redirect URI</p>
                <p className="text-sm text-gray-600 mb-2">In the "Web" section, add this redirect URI:</p>
                <div className="flex items-center gap-2 mb-2">
                  <code className="bg-green-100 px-2 py-1 rounded text-sm flex-1">{redirectUri}</code>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(redirectUri, 'Redirect URI')}
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-xs text-green-700">
                  <strong>Important:</strong> Make sure to click "Save" after adding the URI!
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
              <div className="bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">5</div>
              <div className="flex-1">
                <p className="font-medium">Test Authentication</p>
                <p className="text-sm text-gray-600">After saving, return to the invoice app and try the OneDrive authentication again</p>
              </div>
            </div>
          </div>
        </div>

        {/* Alternative Solution */}
        <div className="border-t pt-4">
          <h4 className="font-semibold mb-2">Alternative Solution</h4>
          <p className="text-sm text-gray-600 mb-3">
            If you can't modify Azure settings, you can use the device code authentication method which doesn't require redirect URIs.
          </p>
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              When prompted for authentication, choose "Cancel" to use the device code method instead of popup authentication.
            </AlertDescription>
          </Alert>
        </div>

        {/* Quick Test */}
        <div className="border-t pt-4">
          <h4 className="font-semibold mb-2">Quick Test</h4>
          <p className="text-sm text-gray-600 mb-3">
            You can test the OneDrive integration on the dedicated test page:
          </p>
          <Button
            variant="outline"
            onClick={() => window.open('/onedrive-test', '_blank')}
          >
            <ExternalLink className="h-3 w-3 mr-1" />
            Open OneDrive Test Page
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default OneDriveSetupInstructions;
