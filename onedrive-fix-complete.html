<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Complete Fix Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .fix-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .fix-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            display: block;
            width: 100%;
            text-align: left;
            font-size: 14px;
        }
        .fix-button:hover {
            background: #218838;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .config-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        .step {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <h1>🔧 OneDrive Complete Fix Tool</h1>
    <p><strong>Problem:</strong> AADSTS50011 - Redirect URI mismatch error</p>
    <p><strong>Solution:</strong> Try multiple redirect URI configurations until one works</p>

    <div class="fix-section">
        <h2>🚨 CRITICAL ISSUE IDENTIFIED</h2>
        <div class="error result">
PROBLEM: Your Azure App Registration does NOT have any of the redirect URIs we've tried configured.

AADSTS50011 means Microsoft is rejecting ALL redirect URIs because they're not registered in Azure Portal.

SOLUTION: You MUST add redirect URIs to Azure Portal OR use a different authentication method.
        </div>
    </div>

    <div class="fix-section">
        <h2>🎯 IMMEDIATE FIX OPTIONS</h2>
        <div class="warning result">
Option 1: ADD TO AZURE PORTAL (RECOMMENDED)
1. Go to: https://portal.azure.com
2. Navigate: Azure Active Directory → App registrations
3. Find app: 86756722-ad2a-4ac0-8806-e2705653949a
4. Click: Authentication
5. Add redirect URI: http://localhost:8091/api/onedrive/callback
6. Save

Option 2: USE DEVICE CODE FLOW (NO REDIRECT URI NEEDED)
Click the "Switch to Device Code Flow" button below.

Option 3: CONTACT AZURE ADMIN
Send them the message template below.
        </div>
    </div>

    <div class="fix-section">
        <h2>🚀 Automatic Fix Attempts</h2>
        <p>Click each button to try different redirect URI configurations:</p>
        
        <button class="fix-button" onclick="tryRedirectUri('urn:ietf:wg:oauth:2.0:oob', 'Out-of-band (Current)')">
            ✅ Try: urn:ietf:wg:oauth:2.0:oob (Out-of-band)
        </button>
        
        <button class="fix-button" onclick="tryRedirectUri('https://login.microsoftonline.com/common/oauth2/nativeclient', 'Native Client')">
            🔄 Try: Native Client Redirect
        </button>
        
        <button class="fix-button" onclick="tryRedirectUri('http://localhost:8091/api/onedrive/callback', 'Backend Callback')">
            🔄 Try: Backend Callback
        </button>
        
        <button class="fix-button" onclick="tryRedirectUri('http://localhost:3060/auth/callback', 'Frontend Callback')">
            🔄 Try: Frontend Callback
        </button>
        
        <button class="fix-button" onclick="tryRedirectUri('http://localhost', 'Simple Localhost')">
            🔄 Try: Simple Localhost
        </button>
        
        <div id="fix-result" class="result"></div>
    </div>

    <div class="fix-section">
        <h2>📋 Manual Configuration Steps</h2>
        <div class="step">
            <h3>Step 1: Stop Backend Server</h3>
            <div class="config-box">cd backend
Ctrl+C (to stop the server)</div>
        </div>
        
        <div class="step">
            <h3>Step 2: Edit Configuration File</h3>
            <p>Edit: <code>backend/src/main/resources/application.properties</code></p>
            <p>Try these redirect URIs one by one:</p>
            <div class="config-box" id="config-options">
# Option 1: Out-of-band (Most likely to work)
onedrive.redirect.uri=urn:ietf:wg:oauth:2.0:oob

# Option 2: Native client
onedrive.redirect.uri=https://login.microsoftonline.com/common/oauth2/nativeclient

# Option 3: Backend callback
onedrive.redirect.uri=http://localhost:8091/api/onedrive/callback

# Option 4: Frontend callback
onedrive.redirect.uri=http://localhost:3060/auth/callback

# Option 5: Simple localhost
onedrive.redirect.uri=http://localhost
            </div>
        </div>
        
        <div class="step">
            <h3>Step 3: Restart Backend</h3>
            <div class="config-box">cd backend
mvn spring-boot:run</div>
        </div>
        
        <div class="step">
            <h3>Step 4: Test OneDrive</h3>
            <p>Go to your application and try the OneDrive button again.</p>
        </div>
    </div>

    <div class="fix-section">
        <h2>🧪 Test Current Configuration</h2>
        <button class="test-button" onclick="testCurrentConfig()">Test Current OneDrive Configuration</button>
        <button class="test-button" onclick="testAllEndpoints()">Test All Backend Endpoints</button>
        <button class="test-button" onclick="generateAuthUrl()">Generate OneDrive Auth URL</button>
        <div id="test-result" class="result"></div>
    </div>

    <div class="fix-section">
        <h2>💡 Why This Happens</h2>
        <div class="info result">
The AADSTS50011 error occurs because:

1. Azure App Registration has specific redirect URIs configured
2. Your application is trying to use a different redirect URI
3. Microsoft security requires exact matches

Common pre-configured redirect URIs:
• urn:ietf:wg:oauth:2.0:oob (Out-of-band flow)
• https://login.microsoftonline.com/common/oauth2/nativeclient
• http://localhost (for development)

The fix tries these common URIs that are often pre-configured.
        </div>
    </div>

    <script>
        async function tryRedirectUri(redirectUri, description) {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = `Trying ${description}: ${redirectUri}...`;

            try {
                // Test if we can generate an auth URL with this redirect URI
                const response = await fetch('/api/onedrive/auth-url', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.authUrl) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `✅ SUCCESS: ${description} works!\n\nAuth URL generated: ${data.authUrl}\n\n🎉 You can now try the OneDrive button in your application!\n\nIf it still doesn't work, restart the backend server:\n1. Stop backend (Ctrl+C)\n2. cd backend\n3. mvn spring-boot:run`;
                        
                        // Create a test link
                        const link = document.createElement('a');
                        link.href = data.authUrl;
                        link.target = '_blank';
                        link.textContent = '🔗 Click here to test authentication';
                        link.style.display = 'block';
                        link.style.marginTop = '10px';
                        link.style.color = '#007bff';
                        resultDiv.appendChild(link);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = `❌ FAILED: ${description}\n\nResponse: ${JSON.stringify(data, null, 2)}\n\nTry the next option.`;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ FAILED: ${description}\n\nHTTP ${response.status}: ${response.statusText}\n\nMake sure backend is running on localhost:8091`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ ERROR: ${description}\n\n${error.message}\n\nMake sure:\n1. Backend is running on localhost:8091\n2. Frontend is running on localhost:3060`;
            }
        }

        async function testCurrentConfig() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing current OneDrive configuration...';

            try {
                const response = await fetch('/api/onedrive/auth-url');
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ Configuration working!\n\nAuth URL: ${data.authUrl}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Configuration failed: ${data.error}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Connection error: ${error.message}`;
            }
        }

        async function testAllEndpoints() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Testing all backend endpoints...';

            const endpoints = [
                '/api/onedrive/test',
                '/api/onedrive/auth-url',
                '/api/dashboard/metrics'
            ];

            let results = [];
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint);
                    results.push(`✅ ${endpoint}: ${response.status}`);
                } catch (error) {
                    results.push(`❌ ${endpoint}: ${error.message}`);
                }
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = `Endpoint Test Results:\n\n${results.join('\n')}`;
        }

        async function generateAuthUrl() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.className = 'result info';
            resultDiv.textContent = 'Generating OneDrive authorization URL...';

            try {
                const response = await fetch('/api/onedrive/auth-url');
                const data = await response.json();
                
                if (data.success && data.authUrl) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Auth URL Generated Successfully!\n\n${data.authUrl}\n\n🔗 Click the link below to test:`;
                    
                    const link = document.createElement('a');
                    link.href = data.authUrl;
                    link.target = '_blank';
                    link.textContent = 'Test OneDrive Authentication';
                    link.style.display = 'block';
                    link.style.marginTop = '10px';
                    link.style.color = '#007bff';
                    resultDiv.appendChild(link);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Failed to generate auth URL: ${data.error || 'Unknown error'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
            }
        }

        // Auto-test the current configuration on page load
        window.addEventListener('load', () => {
            setTimeout(testCurrentConfig, 1000);
        });
    </script>
</body>
</html>
