package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.dto.ClientDto;
import com.redberyl.invoiceapp.entity.Client;
import com.redberyl.invoiceapp.exception.CustomException;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.exception.NullConstraintViolationException;
import com.redberyl.invoiceapp.exception.ResourceNotFoundException;
import com.redberyl.invoiceapp.exception.UniqueConstraintViolationException;
import com.redberyl.invoiceapp.repository.ClientRepository;
import com.redberyl.invoiceapp.service.ClientService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ClientRepository clientRepository;

    @Override
    public List<ClientDto> getAllClients() {
        List<Client> clients = clientRepository.findAll();
        if (clients.isEmpty()) {
            throw new NoContentException("No clients found");
        }
        return clients.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public ClientDto getClientById(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Client ID cannot be null");
        }

        return clientRepository.findById(id)
                .map(this::convertToDto)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + id));
    }

    @Override
    @Transactional
    public ClientDto createClient(ClientDto clientDto) {
        log.info("Creating new client with data: {}", clientDto);
        validateClientDto(clientDto);

        try {
            // Convert DTO to entity
            Client client = convertToEntity(clientDto);
            log.info("Client entity created: {}", client);

            // Save client
            Client savedClient = clientRepository.save(client);
            log.info("Client saved successfully with ID: {}", savedClient.getId());

            // Convert back to DTO and return
            ClientDto resultDto = convertToDto(savedClient);
            log.info("Returning client DTO: {}", resultDto);
            return resultDto;
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";
            log.error("Data integrity violation when creating client: {}", message, e);

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "Client name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Client with this name already exists");
            } else {
                throw new CustomException("Error creating client: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("Error creating client", e);
            throw new CustomException("Error creating client", e);
        }
    }

    private void validateClientDto(ClientDto clientDto) {
        if (clientDto == null) {
            throw new NullConstraintViolationException("clientDto", "Client data cannot be null");
        }

        if (!StringUtils.hasText(clientDto.getName())) {
            throw new NullConstraintViolationException("name", "Client name cannot be empty");
        }
    }

    @Override
    @Transactional
    public ClientDto updateClient(Long id, ClientDto clientDto) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Client ID cannot be null");
        }

        validateClientDto(clientDto);

        Client existingClient = clientRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Client not found with id: " + id));

        try {
            // Update all fields
            existingClient.setName(clientDto.getName());
            existingClient.setEmail(clientDto.getEmail());
            existingClient.setPhone(clientDto.getPhone());
            existingClient.setContactPerson(clientDto.getContactPerson());
            existingClient.setWebsite(clientDto.getWebsite());
            existingClient.setIndustry(clientDto.getIndustry());
            existingClient.setBdmId(clientDto.getBdmId());
            existingClient.setCommissionPercentage(clientDto.getCommissionPercentage());
            existingClient.setBillingAddress(clientDto.getBillingAddress());
            existingClient.setShippingAddress(clientDto.getShippingAddress());
            existingClient.setGstNumber(clientDto.getGstNumber());
            existingClient.setPanNumber(clientDto.getPanNumber());
            existingClient.setCinNumber(clientDto.getCinNumber());
            existingClient.setNotes(clientDto.getNotes());

            Client updatedClient = clientRepository.save(existingClient);
            return convertToDto(updatedClient);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("null") || message.contains("not-null")) {
                throw new NullConstraintViolationException("name", "Client name cannot be null");
            } else if (message.contains("unique") || message.contains("duplicate")) {
                throw new UniqueConstraintViolationException("name", "Client with this name already exists");
            } else {
                throw new CustomException("Error updating client: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error updating client", e);
        }
    }

    @Override
    @Transactional
    public void deleteClient(Long id) {
        if (id == null) {
            throw new NullConstraintViolationException("id", "Client ID cannot be null");
        }

        if (!clientRepository.existsById(id)) {
            throw new ResourceNotFoundException("Client not found with id: " + id);
        }

        try {
            clientRepository.deleteById(id);
        } catch (DataIntegrityViolationException e) {
            String message = e.getMessage() != null ? e.getMessage().toLowerCase() : "";

            if (message.contains("foreign key") || message.contains("reference") ||
                    message.contains("constraint") || message.contains("integrity")) {
                throw new CustomException("Cannot delete client because it is referenced by other entities", e);
            } else {
                throw new CustomException("Error deleting client: " + e.getMessage(), e);
            }
        } catch (Exception e) {
            throw new CustomException("Error deleting client", e);
        }
    }

    private ClientDto convertToDto(Client client) {
        log.info("Converting Client entity to DTO. Client ID: {}", client.getId());

        ClientDto.ClientDtoBuilder builder = ClientDto.builder()
                .id(client.getId())
                .name(client.getName())
                .email(client.getEmail())
                .phone(client.getPhone())
                .contactPerson(client.getContactPerson())
                .website(client.getWebsite())
                .industry(client.getIndustry())
                .bdmId(client.getBdmId())
                .commissionPercentage(client.getCommissionPercentage())
                .billingAddress(client.getBillingAddress())
                .shippingAddress(client.getShippingAddress())
                .gstNumber(client.getGstNumber())
                .panNumber(client.getPanNumber())
                .cinNumber(client.getCinNumber())
                .notes(client.getNotes());

        ClientDto dto = builder.build();

        // Set the audit fields from BaseEntity
        dto.setCreatedAt(client.getCreatedAt());
        dto.setUpdatedAt(client.getModifiedAt());

        log.info("Converted Client to DTO: {}", dto);

        return dto;
    }

    private Client convertToEntity(ClientDto clientDto) {
        log.info("Converting ClientDto to Entity. ClientDto: {}", clientDto);

        // Build the client entity
        Client.ClientBuilder builder = Client.builder()
                .name(clientDto.getName())
                .email(clientDto.getEmail())
                .phone(clientDto.getPhone())
                .contactPerson(clientDto.getContactPerson())
                .website(clientDto.getWebsite())
                .industry(clientDto.getIndustry())
                .bdmId(clientDto.getBdmId())
                .commissionPercentage(clientDto.getCommissionPercentage())
                .billingAddress(clientDto.getBillingAddress())
                .shippingAddress(clientDto.getShippingAddress())
                .gstNumber(clientDto.getGstNumber())
                .panNumber(clientDto.getPanNumber())
                .cinNumber(clientDto.getCinNumber())
                .notes(clientDto.getNotes());

        if (clientDto.getId() != null) {
            builder.id(clientDto.getId());
        }

        Client client = builder.build();
        return client;
    }
}
