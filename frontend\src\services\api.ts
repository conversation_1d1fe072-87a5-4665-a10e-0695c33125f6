// Import utility functions for API URLs
import { getProxiedUrl, getProxiedUrls, AUTH_CONFIG, DEFAULT_HEADERS, getBasicAuthHeader } from '@/utils/apiUtils';

// Re-export for convenience
export { DEFAULT_HEADERS };

// Add a timeout to fetch requests
const fetchWithTimeout = async (url: string, options: RequestInit = {}, timeout = 5000) => {
  const controller = new AbortController();
  const { signal } = controller;

  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
};

// Generic fetch function with error handling
async function fetchApi<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  try {
    // Convert the endpoint to a proxied URL
    const proxiedUrl = getProxiedUrl(endpoint);
    console.log(`Original endpoint: ${endpoint} -> Proxied URL: ${proxiedUrl}`);

    // Create basic auth header
    const authHeader = getBasicAuthHeader();

    // Log the request details for debugging
    console.log('API Request:', {
      url: proxiedUrl,
      method: options.method || 'GET',
      headers: {
        ...DEFAULT_HEADERS,
        'Authorization': authHeader,
        ...options.headers,
      },
      body: options.body,
    });

    // For POST and PUT requests, ensure Content-Type is set
    if ((options.method === 'POST' || options.method === 'PUT') && options.body) {
      options.headers = {
        ...options.headers,
        'Content-Type': 'application/json',
      };

      // Log the request body for debugging
      try {
        const bodyObj = JSON.parse(options.body as string);
        console.log('Request body (parsed):', bodyObj);
      } catch (e) {
        console.log('Request body (raw):', options.body);
      }
    }

    const response = await fetchWithTimeout(
      proxiedUrl,
      {
        headers: {
          ...DEFAULT_HEADERS,
          'Authorization': authHeader,
          ...options.headers,
        },
        credentials: 'include', // Include credentials when using the proxy
        ...options,
      },
      10000 // 10 second timeout
    );

    if (!response.ok) {
      let errorMessage = `API error: ${response.status} ${response.statusText}`;

      // Log the response for debugging
      console.error('API Error Response:', {
        status: response.status,
        statusText: response.statusText,
        url: response.url,
        headers: Object.fromEntries(response.headers.entries()),
      });

      try {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
          const errorData = await response.json();
          console.error('API Error Details (JSON):', errorData);

          // Check if the error is in ApiResponseDto format
          if (errorData && typeof errorData === 'object' && 'success' in errorData && !errorData.success) {
            console.log('Error response is in ApiResponseDto format');
            errorMessage = errorData.message || 'Unknown API error';

            // Create a more detailed error object with the API response
            const error = new Error(errorMessage);
            (error as any).status = response.status;
            (error as any).statusText = response.statusText;
            (error as any).url = response.url;
            (error as any).apiResponse = errorData;
            throw error;
          }

          // Handle other JSON error formats
          if (errorData && errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData && typeof errorData === 'string') {
            errorMessage = errorData;
          } else if (errorData && errorData.error) {
            errorMessage = errorData.error;
          }
        } else {
          // If not JSON, try to get the text
          const errorText = await response.text();
          console.error('API Error (non-JSON):', errorText);

          // If there's text content, include it in the error message
          if (errorText && errorText.trim()) {
            errorMessage += `: ${errorText.trim().substring(0, 100)}${errorText.length > 100 ? '...' : ''}`;
          }
        }
      } catch (e) {
        console.warn('Could not parse error response', e);
      }

      // Create a more detailed error object
      const error = new Error(errorMessage);
      (error as any).status = response.status;
      (error as any).statusText = response.statusText;
      (error as any).url = response.url;
      throw error;
    }

    // For empty responses (like 204 No Content)
    if (response.status === 204) {
      return {} as T;
    }

    try {
      // Get the response text first
      const text = await response.text();

      // Check if the response is empty
      if (!text || text.trim() === '') {
        console.log('Empty response received from API');
        return [] as unknown as T; // Return empty array for empty responses
      }

      // Try to parse the text as JSON
      try {
        const data = JSON.parse(text);
        console.log('API Response data:', data);

        // Check if the response is in ApiResponseDto format
        if (data && typeof data === 'object' && 'success' in data) {
          console.log('Response is in ApiResponseDto format');

          // If it's an error response, throw an error
          if (!data.success) {
            const errorMessage = data.message || 'Unknown API error';
            console.error('API returned error:', errorMessage);
            const error = new Error(errorMessage);
            (error as any).apiResponse = data;
            throw error;
          }

          // Return the data property if it exists
          return data;
        }

        return data;
      } catch (jsonError) {
        console.warn('Could not parse response as JSON:', jsonError, 'Response text:', text);

        // For non-JSON responses, return an empty result
        if (Array.isArray([] as unknown as T)) {
          return [] as unknown as T;
        } else {
          return {} as T;
        }
      }
    } catch (e) {
      console.warn('Could not process response', e);

      // For any error in processing, return an appropriate empty result
      if (Array.isArray([] as unknown as T)) {
        return [] as unknown as T;
      } else {
        return {} as T;
      }
    }
  } catch (error) {
    console.error(`API request failed: ${endpoint}`, error);
    if (error instanceof DOMException && error.name === 'AbortError') {
      throw new Error(`Request timeout: The request to ${endpoint} took too long to complete.`);
    }
    throw error;
  }
}

// Entity-specific API functions
export const api = {
  // Roles
  getRoles: () => fetchApi<any[]>('/auth/roles'),
  getRole: (id: number) => fetchApi<any>(`/auth/roles/${id}`),
  createRole: (data: any) => fetchApi<any>('/auth/roles', {
    method: 'POST',
    body: JSON.stringify(data),
  }),
  updateRole: (id: number, data: any) => fetchApi<any>(`/auth/roles/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  }),
  deleteRole: (id: number) => fetchApi<void>(`/auth/roles/${id}`, {
    method: 'DELETE',
  }),

  // Clients
  getClients: async () => {
    console.log('Fetching all clients from API');

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>('/clients');
      // Check if the response has a data property (ApiResponseDto format)
      const data = response.data || response;
      console.log('Successfully fetched clients from primary endpoint:', data);
      return data;
    } catch (primaryError) {
      console.error('Error fetching clients from primary endpoint:', primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying direct endpoint for clients...');
        // Use the proxy defined in vite.config.ts
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch('/api/clients', {
          method: 'GET',
          headers: {
            ...DEFAULT_HEADERS,
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to fetch clients from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log('Successfully fetched clients from alternative endpoint:', altData);
        return altData;
      } catch (alternativeError) {
        console.error('Error fetching clients from alternative endpoint:', alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },
  getClient: async (id: number) => {
    console.log(`Fetching client with ID ${id}`);

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>(`/clients/${id}`);
      // Check if the response has a data property (ApiResponseDto format)
      const data = response.data || response;
      console.log('Successfully fetched client from primary endpoint:', data);
      return data;
    } catch (primaryError) {
      console.error(`Error fetching client with ID ${id} from primary endpoint:`, primaryError);

      // Try the alternative endpoint without /v1
      try {
        console.log('Trying alternative endpoint for client...');
        // Use the proxy defined in vite.config.ts
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch(`/api/clients/${id}`, {
          method: 'GET',
          headers: {
            ...DEFAULT_HEADERS,
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to fetch client from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log('Successfully fetched client from alternative endpoint:', altData);
        return altData;
      } catch (alternativeError) {
        console.error(`Error fetching client with ID ${id} from alternative endpoint:`, alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },

  // Projects
  getProjects: async () => {
    console.log('Fetching all projects from API');

    // Try multiple endpoints with different approaches, using the proxy
    const endpoints = [
      '/projects/getAll',       // This is the primary endpoint from the screenshot
      '/api/projects',
      '/api/v1/projects',
      '/api/noauth/getProjects',
      '/noauth/projects'
    ];

    // Create basic auth header
    const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);

    // Try each endpoint in sequence
    for (const endpoint of endpoints) {
      try {
        console.log(`API: Trying endpoint ${endpoint} for projects...`);

        // Use a direct fetch to bypass the API_BASE_URL
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        // Handle 204 No Content response
        if (response.status === 204) {
          console.log(`API: Endpoint ${endpoint} returned 204 No Content - no projects available`);
          return []; // Return empty array for 204 responses
        }

        if (!response.ok) {
          console.warn(`API: Endpoint ${endpoint} returned status ${response.status}`);
          continue; // Try the next endpoint
        }

        // Get the response text first to handle empty responses
        const text = await response.text();

        // Check if the response is empty
        if (!text || text.trim() === '') {
          console.log(`API: Empty response received from ${endpoint}`);
          continue; // Try the next endpoint
        }

        try {
          // Try to parse the text as JSON
          const data = JSON.parse(text);
          console.log(`API: Successfully fetched projects from ${endpoint}:`, data);

          // Return the data in the appropriate format
          if (Array.isArray(data)) {
            return data;
          } else if (data && typeof data === 'object') {
            if (Array.isArray(data.data)) {
              return data.data;
            } else if (Array.isArray(data.content)) {
              return data.content;
            } else if (data.data && Array.isArray(data.data.content)) {
              return data.data.content;
            }

            // Try to find any array property
            for (const key in data) {
              if (Array.isArray(data[key])) {
                return data[key];
              }
            }

            // If we get here and data looks like a single project, wrap it in an array
            if (data.id && data.name) {
              console.log(`API: Response appears to be a single project, creating array`);
              return [data];
            }
          }
        } catch (jsonError) {
          console.error(`API: Error parsing JSON from ${endpoint}:`, jsonError, 'Response text:', text);
          continue; // Try the next endpoint
        }
      } catch (endpointError) {
        console.error(`API: Error fetching projects from ${endpoint}:`, endpointError);
      }
    }

    // Try one more approach - no auth
    try {
      console.log('API: Trying no-auth approach for projects...');
      const noAuthResponse = await fetch('/api/projects', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include credentials when using the proxy
      });

      if (noAuthResponse.ok) {
        const text = await noAuthResponse.text();

        if (!text || text.trim() === '') {
          console.log('API: Empty response received from no-auth endpoint');
          return []; // Return empty array for empty responses
        }

        try {
          const noAuthData = JSON.parse(text);
          console.log('API: Successfully fetched projects with no auth:', noAuthData);

          if (Array.isArray(noAuthData)) {
            return noAuthData;
          } else if (noAuthData && typeof noAuthData === 'object') {
            if (Array.isArray(noAuthData.data)) {
              return noAuthData.data;
            } else if (Array.isArray(noAuthData.content)) {
              return noAuthData.content;
            }
          }
        } catch (jsonError) {
          console.error('API: Error parsing JSON from no-auth endpoint:', jsonError);
        }
      }
    } catch (noAuthError) {
      console.error('API: Error fetching projects with no auth:', noAuthError);
    }

    // If all attempts fail, return an empty array instead of throwing
    console.error('API: All approaches to fetch projects failed');
    return [];
  },
  getProject: async (id: number) => {
    console.log(`Fetching project with ID ${id}`);

    // Try multiple endpoints with different approaches, using the proxy
    const endpoints = [
      `/projects/getById/${id}`,  // This is the primary endpoint from the controller
      `/api/projects/${id}`,
      `/api/v1/projects/${id}`,
      `/api/noauth/getProject/${id}`
    ];

    // Create basic auth header
    const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);

    // Try each endpoint in sequence
    for (const endpoint of endpoints) {
      try {
        console.log(`API: Trying endpoint ${endpoint} for project with ID ${id}...`);

        // Use a direct fetch to bypass the API_BASE_URL
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!response.ok) {
          console.warn(`API: Endpoint ${endpoint} returned status ${response.status}`);
          continue; // Try the next endpoint
        }

        // Get the response text first to handle empty responses
        const text = await response.text();

        // Check if the response is empty
        if (!text || text.trim() === '') {
          console.log(`API: Empty response received from ${endpoint}`);
          continue; // Try the next endpoint
        }

        try {
          // Try to parse the text as JSON
          const data = JSON.parse(text);
          console.log(`API: Successfully fetched project with ID ${id} from ${endpoint}:`, data);

          // Return the data directly
          return data;
        } catch (jsonError) {
          console.error(`API: Error parsing JSON from ${endpoint}:`, jsonError, 'Response text:', text);
          continue; // Try the next endpoint
        }
      } catch (endpointError) {
        console.error(`API: Error fetching project with ID ${id} from ${endpoint}:`, endpointError);
      }
    }

    // If all attempts fail, throw an error
    throw new Error(`Could not fetch project with ID ${id} from any endpoint`);
  },

  // Candidates
  getCandidates: () => fetchApi<any[]>('/candidates'),
  getCandidate: (id: number) => fetchApi<any>(`/candidates/${id}`),

  // SPOCs
  getSpocs: () => fetchApi<any[]>('/spocs'),
  getSpoc: (id: number) => fetchApi<any>(`/spocs/${id}`),

  // BDMs
  getBdms: async () => {
    console.log('Fetching all BDMs from API');

    // Try the v1 endpoint first (which is known to work)
    try {
      console.log('Trying v1 endpoint for BDMs first...');
      const response = await fetchApi<any>('/v1/bdms');
      console.log('Raw response from v1 endpoint:', response);

      // Check if the response has a data property (ApiResponseDto format)
      if (response && typeof response === 'object' && 'data' in response) {
        console.log('BDMs response has data property:', response.data);

        // If data is a Page object with content
        if (response.data && typeof response.data === 'object' && 'content' in response.data) {
          console.log('BDMs data is a Page object with content:', response.data.content);
          return response.data.content;
        }

        return response.data;
      }

      // If we couldn't extract BDMs from v1, try the regular endpoint
      console.log('Could not extract BDMs from v1 response, trying regular endpoint');
    } catch (v1Error) {
      console.error('Error fetching BDMs from v1 endpoint:', v1Error);
    }

    // Try the regular endpoint as fallback
    try {
      const response = await fetchApi<any>('/bdms');
      console.log('Raw BDMs response from regular endpoint:', response);

      // If it's an array, return it directly
      if (Array.isArray(response)) {
        console.log('Found BDMs array in response:', response);
        return response;
      }

      // Check if the response has a data property (ApiResponseDto format)
      if (response && typeof response === 'object') {
        if ('data' in response) {
          console.log('BDMs response has data property:', response.data);

          // If data is an array, return it
          if (Array.isArray(response.data)) {
            return response.data;
          }

          // If data is a Page object with content
          if (response.data && typeof response.data === 'object' && 'content' in response.data) {
            console.log('BDMs data is a Page object with content:', response.data.content);
            return response.data.content;
          }
        }

        // Check for content property directly
        if ('content' in response) {
          console.log('Found BDMs in content property:', response.content);
          return response.content;
        }
      }

      // If we couldn't extract BDMs, try the v1 endpoint
      console.log('Could not extract BDMs from response, trying v1 endpoint');
      throw new Error('Could not extract BDMs from response');
    } catch (primaryError) {
      console.error('Error fetching BDMs from primary endpoint:', primaryError);

      // Try the v1 endpoint
      try {
        console.log('Trying v1 endpoint for BDMs...');
        const response = await fetchApi<any>('/v1/bdms');
        console.log('Raw response from v1 endpoint:', response);

        // Check if the response has a data property (ApiResponseDto format)
        if (response && typeof response === 'object' && 'data' in response) {
          console.log('BDMs response has data property:', response.data);

          // If data is a Page object with content
          if (response.data && typeof response.data === 'object' && 'content' in response.data) {
            console.log('BDMs data is a Page object with content:', response.data.content);
            return response.data.content;
          }

          return response.data;
        }

        // If we couldn't extract BDMs, try a direct fetch
        console.log('Could not extract BDMs from v1 response, trying direct fetch');
        throw new Error('Could not extract BDMs from v1 response');
      } catch (v1Error) {
        console.error('Error fetching BDMs from v1 endpoint:', v1Error);

        // Try a direct fetch as a last resort
        try {
          console.log('Trying direct fetch for BDMs...');
          const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
          const directResponse = await fetch('/bdms', {
            method: 'GET',
            headers: {
              ...DEFAULT_HEADERS,
              'Authorization': authHeader
            },
            credentials: 'include' // Include credentials when using the proxy
          });

          if (!directResponse.ok) {
            throw new Error(`Failed to fetch BDMs from direct endpoint: ${directResponse.status}`);
          }

          const directData = await directResponse.json();
          console.log('Successfully fetched BDMs from direct endpoint:', directData);
          return directData;
        } catch (directError) {
          console.error('Error fetching BDMs from direct endpoint:', directError);

          // If all endpoints fail, throw the original error
          throw primaryError;
        }
      }
    }
  },
  getBdm: (id: number) => {
    console.log(`Fetching BDM with ID ${id}`);
    return fetchApi<any>(`/bdms/${id}`)
      .then(response => {
        // Check if the response has a data property (ApiResponseDto format)
        const data = response.data || response;
        console.log('Successfully fetched BDM:', data);
        return data;
      })
      .catch(error => {
        console.error(`Error fetching BDM with ID ${id}:`, error);
        throw error;
      });
  },
  createBdm: async (data: any) => {
    console.log('API createBdm called with data:', data);

    try {
      // Ensure commissionRate is properly formatted for the backend
      const processedData = {
        ...data,
        // Format commission rate as a number
        commissionRate: data.commissionRate !== undefined ?
          (typeof data.commissionRate === 'string' ?
            parseFloat(data.commissionRate) : data.commissionRate) : 0,
        // Ensure these fields are null or not included
        clientCount: null,
        projectCount: null
      };

      // Validate GST Number if provided
      if (processedData.gstNumber) {
        // Ensure it's a string and properly formatted
        processedData.gstNumber = String(processedData.gstNumber).replace(/[^a-zA-Z0-9]/g, '');
      } else if (processedData.gstNumber === '') {
        processedData.gstNumber = null;
      }

      // Ensure email is properly formatted
      if (processedData.email === '') {
        processedData.email = null;
      }

      // Ensure phone is properly formatted
      if (processedData.phone === '') {
        processedData.phone = null;
      }

      // Ensure notes is properly formatted
      if (processedData.notes === '') {
        processedData.notes = null;
      }

      // Ensure billingAddress is properly formatted
      if (processedData.billingAddress === '') {
        processedData.billingAddress = null;
      }

      // Log the full request details for debugging
      console.log('Processed data for API call:', processedData);
      console.log('API URL for creating BDM:', getProxiedUrl('/bdms'));
      console.log('Request method:', 'POST');
      console.log('Request headers:', {
        ...DEFAULT_HEADERS,
        'Authorization': 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`)
      });

      try {
        // Try the v1 endpoint first (which is known to work)
        try {
          console.log('Trying v1 endpoint for creating BDM...');
          const response = await fetchApi<any>('/v1/bdms', {
            method: 'POST',
            body: JSON.stringify(processedData),
          });

          // Log the full response for debugging
          console.log('Raw BDM creation response:', response);

          // Check if the response is in ApiResponseDto format
          if (response && typeof response === 'object' && 'data' in response && 'success' in response) {
            console.log('BDM creation response is in ApiResponseDto format');

            if (response.success) {
              console.log('BDM created successfully:', response.data);
              return response.data;
            } else {
              // If it's an error response, throw an error
              const errorMessage = response.message || 'Unknown error creating BDM';
              console.error('API returned error for BDM creation:', errorMessage);
              const error = new Error(errorMessage);
              (error as any).apiResponse = response;
              throw error;
            }
          } else {
            // If it's not in ApiResponseDto format, use the response directly
            console.log('BDM created successfully:', response);
            return response;
          }
        } catch (primaryError) {
          console.error('Error creating BDM with primary endpoint:', primaryError);

          // Try direct fetch as a fallback
          console.log('Trying direct fetch to create BDM...');
          const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);

          // Try both endpoints, prioritizing the working ones
          const endpoints = [
            '/bdms',        // BdmCompatController - should work
            '/v1/bdms'      // BdmController (backend) - should work
          ];

          for (const endpoint of endpoints) {
            try {
              console.log(`Trying to create BDM with endpoint: ${endpoint}`);
              const directResponse = await fetch(endpoint, {
                method: 'POST',
                headers: {
                  'Accept': 'application/json',
                  'Content-Type': 'application/json',
                  'Authorization': authHeader
                },
                body: JSON.stringify(processedData),
                credentials: 'include'
              });

              if (!directResponse.ok) {
                console.warn(`Failed to create BDM with endpoint ${endpoint}: ${directResponse.status}`);
                continue;
              }

              const data = await directResponse.json();
              console.log(`Successfully created BDM with endpoint ${endpoint}:`, data);

              // Return the data or extract it from ApiResponseDto format
              if (data && typeof data === 'object' && 'data' in data && 'success' in data) {
                return data.data;
              }
              return data;
            } catch (endpointError) {
              console.error(`Error creating BDM with endpoint ${endpoint}:`, endpointError);
            }
          }

          // If all endpoints fail, throw the original error
          throw primaryError;
        }
      } catch (error) {
        console.error('All attempts to create BDM failed:', error);
        throw error;
      }
    } catch (error) {
      console.error('Error processing BDM data for API call:', error);
      throw new Error('Failed to process BDM data for API call. Please check your inputs and try again.');
    }
  },
  updateBdm: (id: number, data: any) => {
    console.log(`API updateBdm called for ID ${id} with data:`, data);
    // Ensure commissionRate is properly formatted for the backend
    const processedData = {
      ...data,
      commissionRate: data.commissionRate !== undefined ?
        (typeof data.commissionRate === 'string' ?
          parseFloat(data.commissionRate) : data.commissionRate) : 0
    };
    console.log('Processed data for API call:', processedData);
    return fetchApi<any>(`/bdms/${id}`, {
      method: 'PUT',
      body: JSON.stringify(processedData),
    })
    .then(response => {
      // Check if the response has a data property (ApiResponseDto format)
      const result = response.data || response;
      console.log('BDM updated successfully:', result);
      return result;
    })
    .catch(error => {
      console.error(`Error updating BDM with ID ${id}:`, error);
      throw error;
    });
  },
  deleteBdm: (id: number) => {
    console.log(`Deleting BDM with ID ${id}`);
    return fetchApi<any>(`/bdms/${id}`, {
      method: 'DELETE',
    })
    .then(response => {
      // Check if the response has a data property (ApiResponseDto format)
      console.log(`BDM with ID ${id} deleted successfully:`, response);
      return response;
    })
    .catch(error => {
      console.error(`Error deleting BDM with ID ${id}:`, error);
      throw error;
    });
  },

  // Staffing Types
  getStaffingTypes: async () => {
    console.log('API: Fetching all staffing types');

    // Try multiple endpoints
    const endpoints = [
      '/staffing-types/getAll',
      '/staffing-types',
      '/v1/staffing-types'
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`API: Trying endpoint: ${endpoint}`);
        const response = await fetchApi<any[]>(endpoint);
        console.log(`API: Successfully fetched staffing types from ${endpoint}:`, response);
        return response;
      } catch (error) {
        console.warn(`API: Error fetching from ${endpoint}:`, error);
      }
    }

    // If all endpoints fail, throw an error
    throw new Error('Failed to fetch staffing types from any endpoint');
  },
  getStaffingType: async (id: number) => {
    console.log(`API: Fetching staffing type with ID ${id}`);

    // Try multiple endpoints
    const endpoints = [
      `/staffing-types/getById/${id}`,
      `/staffing-types/${id}`,
      `/v1/staffing-types/${id}`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`API: Trying endpoint: ${endpoint}`);
        const response = await fetchApi<any>(endpoint);
        console.log(`API: Successfully fetched staffing type from ${endpoint}:`, response);
        return response;
      } catch (error) {
        console.warn(`API: Error fetching from ${endpoint}:`, error);
      }
    }

    // If all endpoints fail, throw an error
    throw new Error(`Failed to fetch staffing type with ID ${id} from any endpoint`);
  },

  // Invoice Types
  getInvoiceTypes: () => fetchApi<any>('/invoice-types'),
  getInvoiceType: (id: number) => fetchApi<any>(`/invoice-types/${id}`),

  // HSN Codes
  getHsnCodes: async () => {
    console.log('Fetching all HSN codes from API');

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>('/hsn-codes');
      // Check if the response has a data property (ApiResponseDto format)
      const data = response.data || response;
      console.log('Successfully fetched HSN codes from primary endpoint:', data);
      return data;
    } catch (primaryError) {
      console.error('Error fetching HSN codes from primary endpoint:', primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying direct endpoint for HSN codes...');
        // Use a direct fetch to bypass the API_BASE_URL
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch('/api/hsn-codes', {
          method: 'GET',
          headers: {
            ...DEFAULT_HEADERS,
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to fetch HSN codes from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log('Successfully fetched HSN codes from alternative endpoint:', altData);
        return altData;
      } catch (alternativeError) {
        console.error('Error fetching HSN codes from alternative endpoint:', alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },
  getHsnCode: async (id: number) => {
    console.log(`Fetching HSN code with ID ${id}`);

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>(`/hsn-codes/${id}`);
      // Check if the response has a data property (ApiResponseDto format)
      const data = response.data || response;
      console.log(`Successfully fetched HSN code with ID ${id} from primary endpoint:`, data);
      return data;
    } catch (primaryError) {
      console.error(`Error fetching HSN code with ID ${id} from primary endpoint:`, primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying alternative endpoint for HSN code...');
        // Use a direct fetch to bypass the API_BASE_URL
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch(`/api/hsn-codes/${id}`, {
          method: 'GET',
          headers: {
            ...DEFAULT_HEADERS,
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to fetch HSN code from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log(`Successfully fetched HSN code with ID ${id} from alternative endpoint:`, altData);
        return altData;
      } catch (alternativeError) {
        console.error(`Error fetching HSN code with ID ${id} from alternative endpoint:`, alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },
  createHsnCode: async (data: any) => {
    console.log('Creating new HSN code:', data);

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>('/hsn-codes', {
        method: 'POST',
        body: JSON.stringify(data),
      });
      // Check if the response has a data property (ApiResponseDto format)
      const responseData = response.data || response;
      console.log('Successfully created HSN code from primary endpoint:', responseData);
      return responseData;
    } catch (primaryError) {
      console.error('Error creating HSN code from primary endpoint:', primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying direct endpoint for creating HSN code...');
        // Use a direct fetch to bypass the API_BASE_URL
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch('/api/hsn-codes', {
          method: 'POST',
          headers: {
            ...DEFAULT_HEADERS,
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(data),
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to create HSN code from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log('Successfully created HSN code from alternative endpoint:', altData);
        return altData;
      } catch (alternativeError) {
        console.error('Error creating HSN code from alternative endpoint:', alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },
  updateHsnCode: async (id: number, data: any) => {
    console.log(`Updating HSN code with ID ${id}:`, data);

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>(`/hsn-codes/${id}`, {
        method: 'PUT',
        body: JSON.stringify(data),
      });
      // Check if the response has a data property (ApiResponseDto format)
      const responseData = response.data || response;
      console.log(`Successfully updated HSN code with ID ${id} from primary endpoint:`, responseData);
      return responseData;
    } catch (primaryError) {
      console.error(`Error updating HSN code with ID ${id} from primary endpoint:`, primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying direct endpoint for updating HSN code...');
        // Use a direct fetch to bypass the API_BASE_URL
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch(`/api/hsn-codes/${id}`, {
          method: 'PUT',
          headers: {
            ...DEFAULT_HEADERS,
            'Content-Type': 'application/json',
            'Authorization': authHeader
          },
          body: JSON.stringify(data),
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to update HSN code from alternative endpoint: ${altResponse.status}`);
        }

        const altData = await altResponse.json();
        console.log(`Successfully updated HSN code with ID ${id} from alternative endpoint:`, altData);
        return altData;
      } catch (alternativeError) {
        console.error(`Error updating HSN code with ID ${id} from alternative endpoint:`, alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },
  deleteHsnCode: async (id: number) => {
    console.log(`Deleting HSN code with ID ${id}`);

    // Try the primary endpoint first
    try {
      const response = await fetchApi<any>(`/hsn-codes/${id}`, {
        method: 'DELETE',
      });
      console.log(`Successfully deleted HSN code with ID ${id} from primary endpoint`);
      return response;
    } catch (primaryError) {
      console.error(`Error deleting HSN code with ID ${id} from primary endpoint:`, primaryError);

      // Try the alternative endpoint directly
      try {
        console.log('Trying direct endpoint for deleting HSN code...');
        // Use a direct fetch to bypass the API_BASE_URL
        const authHeader = 'Basic ' + btoa(`${AUTH_CONFIG.username}:${AUTH_CONFIG.password}`);
        const altResponse = await fetch(`/api/hsn-codes/${id}`, {
          method: 'DELETE',
          headers: {
            ...DEFAULT_HEADERS,
            'Authorization': authHeader
          },
          credentials: 'include' // Include credentials when using the proxy
        });

        if (!altResponse.ok) {
          throw new Error(`Failed to delete HSN code from alternative endpoint: ${altResponse.status}`);
        }

        console.log(`Successfully deleted HSN code with ID ${id} from alternative endpoint`);
        return {};
      } catch (alternativeError) {
        console.error(`Error deleting HSN code with ID ${id} from alternative endpoint:`, alternativeError);

        // If both endpoints fail, throw the original error
        throw primaryError;
      }
    }
  },

  // Tax Types
  getTaxTypes: () => fetchApi<any[]>('/tax-types'),
  getTaxType: (id: number) => fetchApi<any>(`/tax-types/${id}`),

  // Tax Rates
  getTaxRates: () => fetchApi<any[]>('/tax-rates'),
  getTaxRate: (id: number) => fetchApi<any>(`/tax-rates/${id}`),

  // Redberyl Accounts
  getRedberylAccounts: () => fetchApi<any[]>('/redberyl-accounts'),
  getRedberylAccount: (id: number) => fetchApi<any>(`/redberyl-accounts/${id}`),

  // Leads
  getLeads: () => fetchApi<any[]>('/leads'),
  getLead: (id: number) => fetchApi<any>(`/leads/${id}`),

  // Deals
  getDeals: () => fetchApi<any[]>('/deals'),
  getDeal: (id: number) => fetchApi<any>(`/deals/${id}`),

  // Communications
  getCommunications: () => fetchApi<any[]>('/communications'),
  getCommunication: (id: number) => fetchApi<any>(`/communications/${id}`),

  // Document Templates
  getDocumentTemplates: () => fetchApi<any[]>('/document-templates'),
  getDocumentTemplate: (id: number) => fetchApi<any>(`/document-templates/${id}`),

  // Document Template Versions
  getDocumentTemplateVersions: () => fetchApi<any[]>('/document-template-versions'),
  getDocumentTemplateVersion: (id: number) => fetchApi<any>(`/document-template-versions/${id}`),

  // Document Variables
  getDocumentVariables: () => fetchApi<any[]>('/document-variables'),
  getDocumentVariable: (id: number) => fetchApi<any>(`/document-variables/${id}`),
};
