import { toast } from 'sonner';

/**
 * Helper function to test OneDrive device code authentication step by step
 */
export const testDeviceCodeAuthentication = async () => {
  console.log('🚀 Starting OneDrive Device Code Authentication Test');
  
  try {
    // Step 1: Start device code flow
    console.log('📋 Step 1: Starting device code flow...');
    const response = await fetch('/api/onedrive/device-code', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const deviceCodeData = await response.json();
    console.log('📋 Device code response:', deviceCodeData);
    
    if (!deviceCodeData.success) {
      throw new Error(deviceCodeData.error || 'Failed to start device code flow');
    }
    
    const { device_code, user_code, verification_uri, interval = 5 } = deviceCodeData;
    
    // Step 2: Show instructions to user
    console.log('📝 Step 2: Showing instructions to user...');
    
    // Copy code to clipboard
    try {
      await navigator.clipboard.writeText(user_code);
      console.log('📋 Code copied to clipboard');
    } catch (e) {
      console.log('⚠️ Could not copy to clipboard');
    }
    
    // Show alert with instructions
    const proceed = window.confirm(
      `OneDrive Authentication - Device Code Method\n\n` +
      `✅ STEP 1: Code copied to clipboard\n` +
      `📋 CODE: ${user_code}\n\n` +
      `✅ STEP 2: Click OK to open Microsoft verification page\n` +
      `🌐 URL: ${verification_uri}\n\n` +
      `✅ STEP 3: Paste the code and sign in\n\n` +
      `✅ STEP 4: Come back to this page\n\n` +
      `The page will automatically detect when you're done!\n\n` +
      `Click OK to continue or Cancel to stop.`
    );
    
    if (!proceed) {
      console.log('❌ User cancelled authentication');
      return { success: false, error: 'User cancelled authentication' };
    }
    
    // Step 3: Open verification page
    console.log('🌐 Step 3: Opening verification page...');
    window.open(verification_uri, '_blank');
    
    toast.info('Authentication Started', {
      description: `Enter code ${user_code} on the Microsoft page. This will update automatically.`,
      duration: 8000
    });
    
    // Step 4: Poll for completion
    console.log('⏳ Step 4: Polling for authentication completion...');
    
    return new Promise((resolve) => {
      let pollCount = 0;
      const maxPolls = Math.floor((15 * 60) / interval); // 15 minutes
      
      const pollInterval = setInterval(async () => {
        try {
          pollCount++;
          console.log(`🔄 Polling attempt ${pollCount}/${maxPolls}...`);
          
          const tokenResponse = await fetch('/api/onedrive/device-token', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ device_code })
          });
          
          const tokenData = await tokenResponse.json();
          console.log(`📡 Poll ${pollCount} response:`, tokenData);
          
          if (tokenData.success && tokenData.access_token) {
            clearInterval(pollInterval);
            console.log('✅ Authentication successful!');
            
            // Store token
            localStorage.setItem('onedrive_access_token', tokenData.access_token);
            
            toast.success('OneDrive Authentication Successful!', {
              description: 'You can now save invoices to OneDrive.'
            });
            
            resolve({
              success: true,
              accessToken: tokenData.access_token
            });
            
          } else if (tokenData.error === 'authorization_pending') {
            console.log(`⏳ Still waiting... (${tokenData.error})`);
            
            // Show reminder every 30 seconds
            if (pollCount % 6 === 0) {
              toast.info('Still waiting for authentication...', {
                description: `Please complete sign-in on Microsoft page. Code: ${user_code}`,
                duration: 5000
              });
            }
            
          } else if (tokenData.error === 'authorization_declined') {
            clearInterval(pollInterval);
            console.log('❌ User declined authorization');
            resolve({
              success: false,
              error: 'Authorization was declined by user'
            });
            
          } else if (tokenData.error === 'expired_token') {
            clearInterval(pollInterval);
            console.log('⏰ Device code expired');
            resolve({
              success: false,
              error: 'Device code expired. Please try again.'
            });
            
          } else {
            clearInterval(pollInterval);
            console.log('❌ Authentication failed:', tokenData.error);
            resolve({
              success: false,
              error: tokenData.error || 'Authentication failed'
            });
          }
          
        } catch (error) {
          clearInterval(pollInterval);
          console.error('❌ Error during polling:', error);
          resolve({
            success: false,
            error: error instanceof Error ? error.message : 'Polling failed'
          });
        }
      }, interval * 1000);
      
      // Timeout after 15 minutes
      setTimeout(() => {
        clearInterval(pollInterval);
        console.log('⏰ Authentication timeout');
        resolve({
          success: false,
          error: 'Authentication timeout. Please try again.'
        });
      }, 15 * 60 * 1000);
    });
    
  } catch (error) {
    console.error('❌ Device code authentication failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Authentication failed'
    };
  }
};

/**
 * Quick test function to check current authentication status
 */
export const checkOneDriveStatus = async () => {
  try {
    const token = localStorage.getItem('onedrive_access_token');
    console.log('🔍 Checking OneDrive status...');
    console.log('📋 Stored token:', token ? `${token.substring(0, 20)}...` : 'None');
    
    if (!token) {
      console.log('❌ No token found');
      return { authenticated: false, message: 'No access token found' };
    }
    
    const response = await fetch('/api/onedrive/check-auth', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    const result = await response.json();
    console.log('📡 Auth check result:', result);
    
    return result;
  } catch (error) {
    console.error('❌ Error checking auth status:', error);
    return { authenticated: false, error: error instanceof Error ? error.message : 'Check failed' };
  }
};

/**
 * Clear OneDrive authentication
 */
export const clearOneDriveAuth = () => {
  localStorage.removeItem('onedrive_access_token');
  console.log('🧹 OneDrive authentication cleared');
  toast.info('OneDrive authentication cleared');
};
