/**
 * Utility functions for handling IP addresses and API URLs
 */

/**
 * Get the base API URL based on the current environment
 * This will use the current hostname (IP or domain) instead of hardcoded values
 * @returns The base API URL
 */
export const getApiBaseUrl = (): string => {
  // Get the current hostname (will be IP address or domain name)
  const hostname = window.location.hostname;

  // For development and testing, always use localhost for consistency
  // This ensures the frontend and backend communicate properly
  if (process.env.NODE_ENV === 'development' && (hostname === 'localhost' || hostname === '127.0.0.1')) {
    // When running locally, use localhost for consistency
    return 'http://localhost:8091';
  }

  // For production or when accessed via IP, use the current hostname
  // This allows the app to work with both localhost and IP addresses
  return `http://${hostname}:8091`;
};

/**
 * Get the API URL with the specified endpoint
 * @param endpoint The API endpoint
 * @returns The full API URL
 */
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();

  // Ensure endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  return `${baseUrl}${formattedEndpoint}`;
};

/**
 * Check if the current hostname is localhost or our specific IP
 * @returns True if the current hostname is localhost, 127.0.0.1, or ************
 */
export const isLocalhost = (): boolean => {
  const hostname = window.location.hostname;
  return hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '************';
};
