package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.DealDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.DealService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Tag(name = "Deal", description = "Deal management API")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS, RequestMethod.HEAD, RequestMethod.PATCH}, maxAge = 3600)
public class DealController {

    @Autowired
    private DealService dealService;

    @GetMapping("deals/getAll")
    @Operation(summary = "Get all deals", description = "Get all deals")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DealDto>> getAllDeals() {
        try {
            List<DealDto> deals = dealService.getAllDeals();
            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/deals/getById/{id}")
    @Operation(summary = "Get deal by ID", description = "Get deal by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deal found"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DealDto> getDealById(@PathVariable Long id) {
        DealDto deal = dealService.getDealById(id);
        return new ResponseEntity<>(deal, HttpStatus.OK);
    }

    @GetMapping("/deals/getByLeadId/{leadId}")
    @Operation(summary = "Get deals by lead ID", description = "Get deals by lead ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found for this lead"),
            @ApiResponse(responseCode = "404", description = "Lead not found"),
            @ApiResponse(responseCode = "400", description = "Invalid lead ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DealDto>> getDealsByLeadId(@PathVariable Long leadId) {
        try {
            List<DealDto> deals = dealService.getDealsByLeadId(leadId);
            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/deals/getByClientId/{clientId}")
    @Operation(summary = "Get deals by client ID", description = "Get deals by client ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found for this client"),
            @ApiResponse(responseCode = "404", description = "Client not found"),
            @ApiResponse(responseCode = "400", description = "Invalid client ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DealDto>> getDealsByClientId(@PathVariable Long clientId) {
        try {
            List<DealDto> deals = dealService.getDealsByClientId(clientId);
            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/deals/getByStatus/{status}")
    @Operation(summary = "Get deals by status", description = "Get deals by status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deals found"),
            @ApiResponse(responseCode = "204", description = "No deals found with this status"),
            @ApiResponse(responseCode = "400", description = "Invalid status supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<DealDto>> getDealsByStatus(@PathVariable String status) {
        try {
            List<DealDto> deals = dealService.getDealsByStatus(status);
            return new ResponseEntity<>(deals, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @PostMapping("/deals/create")
    @Operation(summary = "Create deal", description = "Create deal")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Deal created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DealDto> createDeal(@Valid @RequestBody DealDto dealDto) {
        DealDto createdDeal = dealService.createDeal(dealDto);
        return new ResponseEntity<>(createdDeal, HttpStatus.CREATED);
    }

    @PutMapping("/deals/update/{id}")
    @Operation(summary = "Update deal", description = "Update deal")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Deal updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or foreign key violation"),
            @ApiResponse(responseCode = "404", description = "Deal not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowedHeaders = "*")
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<DealDto> updateDeal(@PathVariable Long id, @Valid @RequestBody DealDto dealDto) {
        DealDto updatedDeal = dealService.updateDeal(id, dealDto);
        return new ResponseEntity<>(updatedDeal, HttpStatus.OK);
    }

    @DeleteMapping("/deals/deleteById/{id}")
    @Operation(summary = "Delete deal", description = "Delete deal")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Deal deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or deal is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "Deal not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteDeal(@PathVariable Long id) {
        dealService.deleteDeal(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
