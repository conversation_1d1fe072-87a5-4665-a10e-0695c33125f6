<!DOCTYPE html>
<html>
<head>
    <title>OneDrive Auth Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 800px; margin: 0 auto; }
        .button { background: #0078d4; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; margin: 10px; }
        .button:hover { background: #106ebe; }
        .url-box { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 15px 0; word-break: break-all; font-family: monospace; }
        .result { padding: 15px; margin: 15px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
    </style>
</head>
<body>
    <h1>🔗 OneDrive Authorization Test</h1>
    
    <div class="warning result">
        <strong>⚠️ IMPORTANT:</strong> Make sure you added this redirect URI to Azure Portal:
        <br><code>http://localhost:8091/api/onedrive/callback</code>
    </div>

    <h3>Step 1: Test Backend</h3>
    <button class="button" onclick="testBackend()">Test Backend Connection</button>
    <div id="backend-result"></div>

    <h3>Step 2: Get Authorization URL</h3>
    <button class="button" onclick="getAuthUrl()">Get Authorization URL</button>
    <div id="auth-url-result"></div>

    <h3>Step 3: Test Authorization</h3>
    <button class="button" onclick="testAuth()" id="test-auth-btn" disabled>Test Authorization Flow</button>
    <div id="auth-result"></div>

    <script>
        let authUrl = '';

        async function testBackend() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.innerHTML = '⏳ Testing backend...';
            
            try {
                const response = await fetch('http://localhost:8091/api/onedrive/test');
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Backend is running!<br><pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Backend error: ${response.status}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Cannot reach backend: ${error.message}`;
            }
        }

        async function getAuthUrl() {
            const resultDiv = document.getElementById('auth-url-result');
            resultDiv.innerHTML = '⏳ Getting authorization URL...';
            
            try {
                const response = await fetch('http://localhost:8091/api/onedrive/auth-url');
                const data = await response.json();
                
                if (data.success && data.authUrl) {
                    authUrl = data.authUrl;
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ Authorization URL generated!<br><div class="url-box">${authUrl}</div>`;
                    document.getElementById('test-auth-btn').disabled = false;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Failed: ${data.error || 'Unknown error'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ Error: ${error.message}`;
            }
        }

        function testAuth() {
            if (!authUrl) {
                alert('Please get authorization URL first!');
                return;
            }

            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '🔗 Opening authorization popup...';
            
            const popup = window.open(authUrl, 'onedrive-auth', 'width=600,height=700');
            
            if (!popup) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ Failed to open popup. Please allow popups.';
                return;
            }

            // Listen for messages from popup
            window.addEventListener('message', function(event) {
                if (event.data.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `✅ SUCCESS! OneDrive authenticated!<br>Access Token: ${event.data.accessToken.substring(0, 50)}...`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ Authentication failed: ${event.data.error}`;
                }
            }, { once: true });

            // Check if popup closed
            const checkClosed = setInterval(() => {
                if (popup.closed) {
                    clearInterval(checkClosed);
                    if (resultDiv.innerHTML.includes('Opening authorization popup')) {
                        resultDiv.className = 'result warning';
                        resultDiv.textContent = '⚠️ Popup was closed. Please try again.';
                    }
                }
            }, 1000);
        }
    </script>
</body>
</html>
