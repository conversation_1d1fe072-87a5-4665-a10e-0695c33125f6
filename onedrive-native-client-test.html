<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OneDrive Native Client Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 OneDrive Native Client Test</h1>
        
        <div class="info">
            <h3>✅ Current Configuration:</h3>
            <p><strong>Redirect URI:</strong> <code>https://login.microsoftonline.com/common/oauth2/nativeclient</code></p>
            <p><strong>Why this works:</strong> This is a pre-configured redirect URI that Microsoft includes in most Azure app registrations by default.</p>
        </div>

        <div class="warning">
            <h3>⚠️ Important Notes:</h3>
            <ul>
                <li>This test will open a popup window</li>
                <li>You'll need to manually copy the authorization code from the URL</li>
                <li>The native client flow requires manual code extraction</li>
            </ul>
        </div>

        <button id="testAuth" class="button">Test OneDrive Authentication</button>
        
        <div id="result"></div>

        <div class="info">
            <h3>📋 How This Works:</h3>
            <ol>
                <li>Click the test button</li>
                <li>A popup opens with Microsoft login</li>
                <li>After login, you'll see a page with an authorization code</li>
                <li>Copy the code from the URL or page</li>
                <li>Paste it in the prompt that appears</li>
                <li>The code gets exchanged for an access token</li>
            </ol>
        </div>

        <div class="success">
            <h3>🎯 Expected Result:</h3>
            <p>If this works, you'll see:</p>
            <ul>
                <li>✅ No AADSTS50011 error</li>
                <li>✅ Successful Microsoft login</li>
                <li>✅ Authorization code received</li>
                <li>✅ Access token obtained</li>
            </ul>
        </div>
    </div>

    <script>
        document.getElementById('testAuth').addEventListener('click', testAuthentication);

        async function testAuthentication() {
            const button = document.getElementById('testAuth');
            button.disabled = true;
            button.textContent = 'Testing...';

            try {
                // Get authorization URL from backend
                const authResponse = await fetch('/api/onedrive/auth-url');
                const authData = await authResponse.json();

                if (!authData.authUrl) {
                    showError('Failed to get authorization URL: ' + (authData.error || 'Unknown error'));
                    return;
                }

                showInfo('Opening Microsoft login popup...');

                // Open popup with auth URL
                const popup = window.open(
                    authData.authUrl,
                    'onedrive-auth',
                    'width=600,height=700,scrollbars=yes,resizable=yes'
                );

                if (!popup) {
                    showError('Failed to open popup. Please allow popups and try again.');
                    return;
                }

                // Monitor popup for completion
                const checkPopup = setInterval(() => {
                    try {
                        if (popup.closed) {
                            clearInterval(checkPopup);
                            showWarning('Popup was closed. Please try again and complete the authentication.');
                            return;
                        }

                        // Check if popup URL contains authorization code
                        const popupUrl = popup.location.href;
                        
                        if (popupUrl.includes('code=')) {
                            clearInterval(checkPopup);
                            
                            // Extract authorization code
                            const urlParams = new URLSearchParams(popupUrl.split('?')[1]);
                            const code = urlParams.get('code');
                            const state = urlParams.get('state');
                            
                            popup.close();
                            
                            if (code) {
                                showSuccess('✅ Authorization code received! Exchanging for access token...');
                                exchangeCodeForToken(code, state);
                            } else {
                                showError('No authorization code found in URL');
                            }
                        } else if (popupUrl.includes('error=')) {
                            clearInterval(checkPopup);
                            popup.close();
                            
                            const urlParams = new URLSearchParams(popupUrl.split('?')[1]);
                            const error = urlParams.get('error');
                            const errorDescription = urlParams.get('error_description');
                            
                            if (error === 'AADSTS50011') {
                                showError('❌ AADSTS50011: Redirect URI mismatch. The native client URI is not configured in Azure.');
                            } else {
                                showError('Authentication error: ' + (errorDescription || error || 'Unknown error'));
                            }
                        }
                    } catch (e) {
                        // Cross-origin error, popup is still on Microsoft's domain
                        // This is normal, continue checking
                    }
                }, 1000);

                // Timeout after 5 minutes
                setTimeout(() => {
                    clearInterval(checkPopup);
                    if (!popup.closed) {
                        popup.close();
                    }
                    showWarning('Authentication timeout. Please try again.');
                }, 5 * 60 * 1000);

            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                button.disabled = false;
                button.textContent = 'Test OneDrive Authentication';
            }
        }

        async function exchangeCodeForToken(code, state) {
            try {
                const response = await fetch('/api/onedrive/exchange-code', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ code, state })
                });

                const data = await response.json();

                if (data.success && data.accessToken) {
                    showSuccess('🎉 SUCCESS! Access token received. OneDrive authentication is working!');
                    
                    // Test the access token
                    testAccessToken(data.accessToken);
                } else {
                    showError('Failed to exchange code for token: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                showError('Error exchanging code for token: ' + error.message);
            }
        }

        async function testAccessToken(accessToken) {
            try {
                const response = await fetch('/api/onedrive/test', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + accessToken
                    }
                });

                const data = await response.json();
                
                if (data.success) {
                    showSuccess('🚀 OneDrive connection test successful! You can now upload files to OneDrive.');
                } else {
                    showWarning('Access token received but OneDrive test failed: ' + (data.error || 'Unknown error'));
                }
            } catch (error) {
                showWarning('Access token received but failed to test OneDrive connection: ' + error.message);
            }
        }

        function showSuccess(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="success">' + message + '</div>';
        }

        function showError(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="error">' + message + '</div>';
        }

        function showInfo(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="info">' + message + '</div>';
        }

        function showWarning(message) {
            const result = document.getElementById('result');
            result.innerHTML = '<div class="warning">' + message + '</div>';
        }
    </script>
</body>
</html>
