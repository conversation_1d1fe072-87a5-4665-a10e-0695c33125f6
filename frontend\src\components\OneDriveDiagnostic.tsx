import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, XCircle, Info } from 'lucide-react';
import { toast } from 'sonner';

const OneDriveDiagnostic: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const runDiagnostic = async () => {
    setIsRunning(true);
    setResults([]);
    
    const diagnosticResults: any[] = [];

    try {
      console.clear();
      console.log('🔍 Starting OneDrive Diagnostic...');

      // Test 1: Check Backend Configuration
      diagnosticResults.push({
        test: 'Backend Configuration',
        status: 'running',
        message: 'Checking backend configuration...'
      });
      setResults([...diagnosticResults]);

      try {
        const configResponse = await fetch('/api/onedrive/auth-url', {
          method: 'GET'
        });

        const configData = await configResponse.json();
        console.log('🔧 Auth URL response:', configData);

        if (configData.success && configData.authUrl) {
          diagnosticResults[0] = {
            test: 'Backend Configuration',
            status: 'success',
            message: 'Backend configuration is working',
            details: `Auth URL generated successfully`
          };
        } else {
          diagnosticResults[0] = {
            test: 'Backend Configuration',
            status: 'error',
            message: 'Backend configuration failed',
            details: configData.error || 'No auth URL generated'
          };
        }
      } catch (error) {
        diagnosticResults[0] = {
          test: 'Backend Configuration',
          status: 'error',
          message: 'Backend configuration error',
          details: error instanceof Error ? error.message : String(error)
        };
      }

      setResults([...diagnosticResults]);

      // Test 2: Device Code Generation
      diagnosticResults.push({
        test: 'Device Code Generation',
        status: 'running',
        message: 'Testing device code generation...'
      });
      setResults([...diagnosticResults]);

      try {
        const deviceResponse = await fetch('/api/onedrive/device-code', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        });

        console.log('📱 Device code response status:', deviceResponse.status);
        
        if (!deviceResponse.ok) {
          const errorText = await deviceResponse.text();
          throw new Error(`HTTP ${deviceResponse.status}: ${errorText}`);
        }

        const deviceData = await deviceResponse.json();
        console.log('📱 Device code data:', deviceData);

        if (deviceData.success && deviceData.device_code) {
          diagnosticResults[1] = {
            test: 'Device Code Generation',
            status: 'success',
            message: 'Device code generated successfully',
            details: `User code: ${deviceData.user_code}, Verification URI: ${deviceData.verification_uri}`
          };

          // Test 3: Token Polling (without user interaction)
          diagnosticResults.push({
            test: 'Token Polling Endpoint',
            status: 'running',
            message: 'Testing token polling endpoint...'
          });
          setResults([...diagnosticResults]);

          try {
            const tokenResponse = await fetch('/api/onedrive/device-token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ device_code: deviceData.device_code })
            });

            console.log('🔄 Token polling response status:', tokenResponse.status);

            if (!tokenResponse.ok) {
              const errorText = await tokenResponse.text();
              throw new Error(`HTTP ${tokenResponse.status}: ${errorText}`);
            }

            const tokenData = await tokenResponse.json();
            console.log('🔄 Token polling data:', tokenData);

            if (tokenData.error === 'authorization_pending') {
              diagnosticResults[2] = {
                test: 'Token Polling Endpoint',
                status: 'success',
                message: 'Token polling endpoint working correctly',
                details: 'Received expected "authorization_pending" response'
              };
            } else if (tokenData.error === 'unauthorized') {
              diagnosticResults[2] = {
                test: 'Token Polling Endpoint',
                status: 'error',
                message: 'Unauthorized error - Azure configuration issue',
                details: 'Check client ID, client secret, and tenant ID in Azure Portal'
              };
            } else {
              diagnosticResults[2] = {
                test: 'Token Polling Endpoint',
                status: 'warning',
                message: 'Unexpected response from token endpoint',
                details: `Response: ${JSON.stringify(tokenData)}`
              };
            }
          } catch (error) {
            diagnosticResults[2] = {
              test: 'Token Polling Endpoint',
              status: 'error',
              message: 'Token polling endpoint failed',
              details: error instanceof Error ? error.message : String(error)
            };
          }

        } else {
          diagnosticResults[1] = {
            test: 'Device Code Generation',
            status: 'error',
            message: 'Device code generation failed',
            details: deviceData.error || 'No device code in response'
          };
        }
      } catch (error) {
        diagnosticResults[1] = {
          test: 'Device Code Generation',
          status: 'error',
          message: 'Device code generation error',
          details: error instanceof Error ? error.message : String(error)
        };
      }

      setResults([...diagnosticResults]);

      // Test 4: Configuration Validation
      diagnosticResults.push({
        test: 'Configuration Validation',
        status: 'running',
        message: 'Validating Azure configuration...'
      });
      setResults([...diagnosticResults]);

      // Check if we can validate the configuration
      const hasUnauthorizedError = diagnosticResults.some(r => 
        r.details && r.details.includes('unauthorized')
      );

      if (hasUnauthorizedError) {
        diagnosticResults[diagnosticResults.length - 1] = {
          test: 'Configuration Validation',
          status: 'error',
          message: 'Azure configuration issues detected',
          details: 'Client ID, Client Secret, or Tenant ID may be incorrect or expired'
        };
      } else {
        diagnosticResults[diagnosticResults.length - 1] = {
          test: 'Configuration Validation',
          status: 'success',
          message: 'Azure configuration appears valid',
          details: 'No unauthorized errors detected'
        };
      }

      setResults([...diagnosticResults]);

      // Summary
      const errorCount = diagnosticResults.filter(r => r.status === 'error').length;
      const warningCount = diagnosticResults.filter(r => r.status === 'warning').length;

      if (errorCount === 0 && warningCount === 0) {
        toast.success('Diagnostic Complete - All Tests Passed', {
          description: 'OneDrive integration should be working correctly'
        });
      } else if (errorCount > 0) {
        toast.error('Diagnostic Complete - Issues Found', {
          description: `${errorCount} error(s) and ${warningCount} warning(s) detected`
        });
      } else {
        toast.warning('Diagnostic Complete - Warnings Found', {
          description: `${warningCount} warning(s) detected`
        });
      }

    } catch (error) {
      console.error('❌ Diagnostic failed:', error);
      toast.error('Diagnostic failed', {
        description: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-500" />
          OneDrive Diagnostic Tool
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-3">
          <Button
            onClick={runDiagnostic}
            disabled={isRunning}
            variant="outline"
            className="gap-2"
          >
            {isRunning ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <AlertTriangle className="h-4 w-4" />
            )}
            {isRunning ? 'Running Diagnostic...' : 'Run Diagnostic'}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-semibold">Diagnostic Results:</h4>
            {results.map((result, index) => (
              <div key={index} className="border rounded-lg p-3">
                <div className="flex items-center gap-2 mb-2">
                  {getStatusIcon(result.status)}
                  <span className="font-medium">{result.test}</span>
                </div>
                <p className="text-sm text-gray-600 mb-1">{result.message}</p>
                {result.details && (
                  <p className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                    {result.details}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Common Unauthorized Issues:</strong>
            <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
              <li>Client Secret expired (Azure secrets expire after 1-2 years)</li>
              <li>Incorrect Client ID or Tenant ID</li>
              <li>App not properly configured in Azure Portal</li>
              <li>Missing API permissions in Azure app registration</li>
            </ul>
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
};

export default OneDriveDiagnostic;
