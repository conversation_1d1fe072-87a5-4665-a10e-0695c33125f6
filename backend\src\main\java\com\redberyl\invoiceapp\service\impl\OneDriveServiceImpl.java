package com.redberyl.invoiceapp.service.impl;

import com.redberyl.invoiceapp.config.OneDriveConfig;
import com.redberyl.invoiceapp.dto.OneDriveUploadResponse;
import com.redberyl.invoiceapp.service.OneDriveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.HttpClientErrorException;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.HashMap;

/**
 * Implementation of OneDrive service using Microsoft Graph API
 */
@Service
public class OneDriveServiceImpl implements OneDriveService {

    private static final Logger logger = LoggerFactory.getLogger(OneDriveServiceImpl.class);
    
    @Autowired
    private OneDriveConfig oneDriveConfig;
    
    private final RestTemplate restTemplate = new RestTemplate();

    @Override
    public String getAuthorizationUrl() {
        try {
            String state = java.util.UUID.randomUUID().toString();

            // Use native client redirect URI which is pre-configured in most Azure apps
            return "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/authorize" +
                    "?client_id=" + java.net.URLEncoder.encode(oneDriveConfig.getClientId(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&response_type=code" +
                    "&redirect_uri=" + java.net.URLEncoder.encode(oneDriveConfig.getRedirectUri(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&scope=" + java.net.URLEncoder.encode(oneDriveConfig.getScope(), java.nio.charset.StandardCharsets.UTF_8) +
                    "&state=" + state +
                    "&response_mode=query" +
                    "&prompt=select_account";
        } catch (Exception e) {
            logger.error("Error generating authorization URL", e);
            throw new RuntimeException("Failed to generate authorization URL", e);
        }
    }

    @Override
    public String handleCallback(String code, String state) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("code", code);
            body.add("redirect_uri", oneDriveConfig.getRedirectUri());
            body.add("grant_type", "authorization_code");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to get access token");
            }
        } catch (Exception e) {
            logger.error("Error handling OAuth callback", e);
            throw new RuntimeException("Failed to handle OAuth callback", e);
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public OneDriveUploadResponse uploadFile(String accessToken, byte[] fileContent, String fileName, String folderPath) {
        try {
            // Create the upload URL - using the correct Microsoft Graph API format
            String uploadUrl = oneDriveConfig.getGraphEndpoint() + "/me/drive/root:" + folderPath + "/" + fileName + ":/content";

            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentLength(fileContent.length);

            HttpEntity<byte[]> request = new HttpEntity<>(fileContent, headers);

            ResponseEntity<Map> response = restTemplate.exchange(uploadUrl, HttpMethod.PUT, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                Map<String, Object> responseBody = response.getBody();
                if (responseBody != null) {
                    String fileId = (String) responseBody.get("id");
                    String webUrl = (String) responseBody.get("webUrl");

                    // Handle download URL safely
                    String downloadUrl = null;
                    if (responseBody.containsKey("@microsoft.graph.downloadUrl")) {
                        downloadUrl = (String) responseBody.get("@microsoft.graph.downloadUrl");
                    }

                    Long fileSize = 0L;
                    if (responseBody.containsKey("size")) {
                        fileSize = ((Number) responseBody.get("size")).longValue();
                    }

                    return OneDriveUploadResponse.success(fileId, fileName, webUrl, downloadUrl, fileSize);
                }
            }

            return OneDriveUploadResponse.error("Upload failed with status: " + response.getStatusCode());
        } catch (HttpClientErrorException e) {
            logger.error("HTTP error uploading file to OneDrive: {} - {}", e.getStatusCode(), e.getResponseBodyAsString());
            return OneDriveUploadResponse.error("Upload failed: " + e.getStatusCode() + " - " + e.getResponseBodyAsString());
        } catch (Exception e) {
            logger.error("Error uploading file to OneDrive", e);
            return OneDriveUploadResponse.error("Upload failed: " + e.getMessage());
        }
    }

    @Override
    public OneDriveUploadResponse uploadInvoicePdf(String accessToken, byte[] pdfContent, String invoiceNumber) {
        try {
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            String fileName = String.format("Invoice_%s_%s.pdf", invoiceNumber, timestamp);
            String folderPath = oneDriveConfig.getBasePath() + "/Invoices";
            
            return uploadFile(accessToken, pdfContent, fileName, folderPath);
        } catch (Exception e) {
            logger.error("Error uploading invoice PDF to OneDrive", e);
            return OneDriveUploadResponse.error("Failed to upload invoice PDF: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public boolean isAuthenticated(String accessToken) {
        try {
            if (accessToken == null || accessToken.trim().isEmpty()) {
                return false;
            }
            
            String testUrl = oneDriveConfig.getGraphEndpoint() + "/me";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setBearerAuth(accessToken);
            
            HttpEntity<String> request = new HttpEntity<>(headers);
            
            ResponseEntity<Map> response = restTemplate.exchange(testUrl, HttpMethod.GET, request, Map.class);
            
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            logger.debug("Token validation failed", e);
            return false;
        }
    }

    @Override
    @SuppressWarnings({"unchecked", "rawtypes"})
    public String refreshAccessToken(String refreshToken) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            
            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("client_secret", oneDriveConfig.getClientSecret());
            body.add("refresh_token", refreshToken);
            body.add("grant_type", "refresh_token");
            body.add("scope", oneDriveConfig.getScope());
            
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);
            
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);
            
            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenResponse = response.getBody();
                return (String) tokenResponse.get("access_token");
            } else {
                throw new RuntimeException("Failed to refresh access token");
            }
        } catch (Exception e) {
            logger.error("Error refreshing access token", e);
            throw new RuntimeException("Failed to refresh access token", e);
        }
    }

    @Override
    public Map<String, Object> startDeviceCodeFlow() {
        try {
            String deviceCodeUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/devicecode";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("scope", oneDriveConfig.getScope());

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(deviceCodeUrl, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> deviceCodeResponse = response.getBody();

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("device_code", deviceCodeResponse.get("device_code"));
                result.put("user_code", deviceCodeResponse.get("user_code"));
                result.put("verification_uri", deviceCodeResponse.get("verification_uri"));
                result.put("expires_in", deviceCodeResponse.get("expires_in"));
                result.put("interval", deviceCodeResponse.get("interval"));
                result.put("message", deviceCodeResponse.get("message"));

                return result;
            } else {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "Failed to start device code flow");
                return error;
            }
        } catch (Exception e) {
            logger.error("Error starting device code flow", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to start device code flow: " + e.getMessage());
            return error;
        }
    }

    @Override
    public Map<String, Object> pollDeviceToken(String deviceCode) {
        try {
            String tokenUrl = "https://login.microsoftonline.com/" + oneDriveConfig.getTenantId() + "/oauth2/v2.0/token";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
            body.add("grant_type", "urn:ietf:params:oauth:grant-type:device_code");
            body.add("client_id", oneDriveConfig.getClientId());
            body.add("device_code", deviceCode);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.postForEntity(tokenUrl, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> tokenResponse = response.getBody();

                Map<String, Object> result = new HashMap<>();
                result.put("success", true);
                result.put("access_token", tokenResponse.get("access_token"));
                result.put("token_type", tokenResponse.get("token_type"));
                result.put("expires_in", tokenResponse.get("expires_in"));
                result.put("scope", tokenResponse.get("scope"));

                return result;
            } else {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "Token not ready yet");
                return error;
            }
        } catch (HttpClientErrorException e) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);

            if (e.getStatusCode() == HttpStatus.BAD_REQUEST) {
                String responseBody = e.getResponseBodyAsString();
                if (responseBody.contains("authorization_pending")) {
                    error.put("error", "authorization_pending");
                } else if (responseBody.contains("authorization_declined")) {
                    error.put("error", "authorization_declined");
                } else if (responseBody.contains("expired_token")) {
                    error.put("error", "expired_token");
                } else {
                    error.put("error", "bad_request");
                }
            } else {
                error.put("error", "Failed to poll device token: " + e.getMessage());
            }

            return error;
        } catch (Exception e) {
            logger.error("Error polling device token", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to poll device token: " + e.getMessage());
            return error;
        }
    }
}
