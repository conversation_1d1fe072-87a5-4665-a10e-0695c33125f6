package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.SpocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Tag(name = "SPOC", description = "SPOC management API")
public class SpocController {

    @Autowired
    private SpocService spocService;

    @GetMapping("/spocs/getAll")
    @Operation(summary = "Get all SPOCs", description = "Get all SPOCs")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOCs found"),
            @ApiResponse(responseCode = "204", description = "No SPOCs found", content = @Content)
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<List<SpocDto>> getAllSpocs() {
        try {
            List<SpocDto> spocs = spocService.getAllSpocs();
            return new ResponseEntity<>(spocs, HttpStatus.OK);
        } catch (NoContentException e) {
            return ResponseEntity.noContent().build();
        }
    }

    @GetMapping("/spocs/getById/{id}")
    @Operation(summary = "Get SPOC by ID", description = "Get SPOC by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOC found"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> getSpocById(@PathVariable Long id) {
        SpocDto spoc = spocService.getSpocById(id);
        return new ResponseEntity<>(spoc, HttpStatus.OK);
    }

    @GetMapping("/spocs/getByEmail/{emailId}")
    @Operation(summary = "Get SPOC by email", description = "Get SPOC by email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOC found"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "400", description = "Invalid email supplied")
    })
    @PreAuthorize("hasRole('USER') or hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> getSpocByEmailId(@PathVariable String emailId) {
        SpocDto spoc = spocService.getSpocByEmailId(emailId);
        return new ResponseEntity<>(spoc, HttpStatus.OK);
    }

    @PostMapping("/spocs/create")
    @Operation(summary = "Create SPOC", description = "Create SPOC")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "SPOC created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> createSpoc(@Valid @RequestBody SpocDto spocDto) {
        SpocDto createdSpoc = spocService.createSpoc(spocDto);
        return new ResponseEntity<>(createdSpoc, HttpStatus.CREATED);
    }

    @PutMapping("/spocs/update/{id}")
    @Operation(summary = "Update SPOC", description = "Update SPOC")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOC updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "700", description = "Null constraint violation"),
            @ApiResponse(responseCode = "701", description = "Unique constraint violation")
    })
    @PreAuthorize("hasRole('MODERATOR') or hasRole('ADMIN')")
    public ResponseEntity<SpocDto> updateSpoc(@PathVariable Long id, @Valid @RequestBody SpocDto spocDto) {
        SpocDto updatedSpoc = spocService.updateSpoc(id, spocDto);
        return new ResponseEntity<>(updatedSpoc, HttpStatus.OK);
    }

    @DeleteMapping("/spocs/deleteById/{id}")
    @Operation(summary = "Delete SPOC", description = "Delete SPOC")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "SPOC deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied or SPOC is referenced by other entities"),
            @ApiResponse(responseCode = "404", description = "SPOC not found")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteSpoc(@PathVariable Long id) {
        spocService.deleteSpoc(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
