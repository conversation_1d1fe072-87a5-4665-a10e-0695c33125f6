package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.dashboard.DashboardMetricsDto;
import com.redberyl.invoiceapp.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060", "http://localhost:5173"},
        allowedHeaders = "*",
        methods = {
                RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.PATCH,
                RequestMethod.DELETE, RequestMethod.OPTIONS, RequestMethod.HEAD
        },
        allowCredentials = "true",
        maxAge = 3600)
@Tag(name = "Dashboard", description = "Dashboard API")
public class DashboardController {

    @Autowired
    private DashboardService dashboardService;

    @GetMapping("/metrics")
    @Operation(summary = "Get dashboard metrics", description = "Get dashboard metrics including invoice, payment, client, and document statistics")
    public ResponseEntity<DashboardMetricsDto> getDashboardMetrics() {
        try {
            System.out.println("=== DASHBOARD METRICS REQUEST ===");
            DashboardMetricsDto metrics = dashboardService.getDashboardMetrics();
            System.out.println("✓ Dashboard metrics generated successfully");
            System.out.println("Total Invoices: " + metrics.getTotalInvoices().getCount());
            System.out.println("Total Payments: " + metrics.getTotalPayments().getAmount());
            System.out.println("Active Clients: " + metrics.getActiveClients().getCount());
            System.out.println("Documents: " + metrics.getDocuments().getCount());

            return ResponseEntity.ok()
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                    .header("Access-Control-Allow-Headers", "*")
                    .body(metrics);
        } catch (Exception e) {
            System.err.println("❌ Error generating dashboard metrics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .header("Access-Control-Allow-Origin", "*")
                    .body(null);
        }
    }

    @GetMapping("/public/metrics")
    @Operation(summary = "Get dashboard metrics (public)", description = "Public endpoint for dashboard metrics - no authentication required")
    public ResponseEntity<DashboardMetricsDto> getPublicDashboardMetrics() {
        try {
            System.out.println("=== PUBLIC DASHBOARD METRICS REQUEST ===");
            DashboardMetricsDto metrics = dashboardService.getDashboardMetrics();
            System.out.println("✓ Public dashboard metrics generated successfully");

            return ResponseEntity.ok()
                    .header("Access-Control-Allow-Origin", "*")
                    .header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
                    .header("Access-Control-Allow-Headers", "*")
                    .body(metrics);
        } catch (Exception e) {
            System.err.println("❌ Error generating public dashboard metrics: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.status(500)
                    .header("Access-Control-Allow-Origin", "*")
                    .body(null);
        }
    }
}
