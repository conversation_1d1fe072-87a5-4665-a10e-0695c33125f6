package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.OneDriveUploadResponse;
import com.redberyl.invoiceapp.service.OneDriveService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for OneDrive integration
 */
@RestController
@RequestMapping("/api/onedrive")
@Tag(name = "OneDrive", description = "OneDrive integration API")
@CrossOrigin(origins = {"http://localhost:3060", "http://127.0.0.1:3060"}, allowCredentials = "true")
public class OneDriveController {

    private static final Logger logger = LoggerFactory.getLogger(OneDriveController.class);

    @Autowired
    private OneDriveService oneDriveService;

    @GetMapping("/auth-url")
    @Operation(summary = "Get OneDrive authorization URL", description = "Get the URL to authorize OneDrive access")
    public ResponseEntity<Map<String, String>> getAuthorizationUrl() {
        try {
            String authUrl = oneDriveService.getAuthorizationUrl();
            Map<String, String> response = new HashMap<>();
            response.put("authUrl", authUrl);
            response.put("success", "true");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error getting authorization URL", e);
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get authorization URL: " + e.getMessage());
            error.put("success", "false");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @GetMapping("/callback")
    @Operation(summary = "Handle OneDrive OAuth callback", description = "Handle the OAuth callback from OneDrive")
    public ResponseEntity<String> handleCallback(
            @RequestParam("code") String code,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "error", required = false) String error) {

        if (error != null) {
            logger.error("OAuth error: {}", error);
            return ResponseEntity.ok(
                "<html><body><script>window.opener.postMessage({success: false, error: '" + error + "'}, '*'); window.close();</script></body></html>"
            );
        }

        try {
            String accessToken = oneDriveService.handleCallback(code, state);
            return ResponseEntity.ok(
                "<html><body><script>window.opener.postMessage({success: true, accessToken: '" + accessToken + "'}, '*'); window.close();</script></body></html>"
            );
        } catch (Exception e) {
            logger.error("Error handling callback", e);
            return ResponseEntity.ok(
                "<html><body><script>window.opener.postMessage({success: false, error: '" + e.getMessage() + "'}, '*'); window.close();</script></body></html>"
            );
        }
    }

    @PostMapping("/exchange-code")
    @Operation(summary = "Exchange authorization code for access token", description = "Exchange the authorization code for an access token")
    public ResponseEntity<Map<String, Object>> exchangeCode(@RequestBody Map<String, String> request) {
        try {
            String code = request.get("code");
            String state = request.get("state");

            Map<String, Object> response = new HashMap<>();

            if (code == null) {
                response.put("success", false);
                response.put("error", "No authorization code provided");
                return ResponseEntity.badRequest().body(response);
            }

            String accessToken = oneDriveService.handleCallback(code, state);
            response.put("success", true);
            response.put("accessToken", accessToken);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error exchanging code for token", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to exchange code: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @PostMapping("/device-code")
    @Operation(summary = "Start device code flow", description = "Start device code flow for OneDrive authentication")
    public ResponseEntity<Map<String, Object>> startDeviceCodeFlow() {
        try {
            Map<String, Object> deviceCodeResponse = oneDriveService.startDeviceCodeFlow();
            return ResponseEntity.ok(deviceCodeResponse);
        } catch (Exception e) {
            logger.error("Error starting device code flow", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to start device code flow: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @PostMapping("/device-token")
    @Operation(summary = "Poll for device token", description = "Poll for access token using device code")
    public ResponseEntity<Map<String, Object>> pollDeviceToken(@RequestBody Map<String, String> request) {
        try {
            String deviceCode = request.get("device_code");

            if (deviceCode == null || deviceCode.trim().isEmpty()) {
                Map<String, Object> error = new HashMap<>();
                error.put("success", false);
                error.put("error", "Device code is required");
                return ResponseEntity.badRequest().body(error);
            }

            logger.info("Polling for device token with device code: {}", deviceCode.substring(0, Math.min(10, deviceCode.length())) + "...");
            Map<String, Object> tokenResponse = oneDriveService.pollDeviceToken(deviceCode);
            return ResponseEntity.ok(tokenResponse);
        } catch (Exception e) {
            logger.error("Error polling device token", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to poll device token: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }



    @PostMapping("/callback")
    @Operation(summary = "Handle OneDrive OAuth callback via POST", description = "Handle the OAuth callback from OneDrive via POST request")
    public ResponseEntity<Map<String, Object>> handleCallbackPost(@RequestBody Map<String, String> request) {
        try {
            String code = request.get("code");
            String state = request.get("state");
            String error = request.get("error");

            Map<String, Object> response = new HashMap<>();

            if (error != null) {
                logger.error("OAuth error: {}", error);
                response.put("success", false);
                response.put("error", error);
                return ResponseEntity.ok(response);
            }

            if (code == null) {
                response.put("success", false);
                response.put("error", "No authorization code provided");
                return ResponseEntity.badRequest().body(response);
            }

            String accessToken = oneDriveService.handleCallback(code, state);
            response.put("success", true);
            response.put("accessToken", accessToken);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("Error handling callback", e);
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("error", "Failed to handle callback: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @PostMapping("/upload-pdf")
    @Operation(summary = "Upload PDF to OneDrive", description = "Upload a PDF file to OneDrive")
    public ResponseEntity<OneDriveUploadResponse> uploadPdf(
            @RequestHeader("Authorization") String authorization,
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "invoiceNumber", required = false) String invoiceNumber) {
        
        try {
            // Extract access token from Authorization header
            String accessToken = authorization.replace("Bearer ", "");
            
            if (!oneDriveService.isAuthenticated(accessToken)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(OneDriveUploadResponse.error("Invalid or expired access token"));
            }

            byte[] fileContent = file.getBytes();
            OneDriveUploadResponse response;
            
            if (invoiceNumber != null && !invoiceNumber.trim().isEmpty()) {
                response = oneDriveService.uploadInvoicePdf(accessToken, fileContent, invoiceNumber);
            } else {
                response = oneDriveService.uploadFile(accessToken, fileContent, file.getOriginalFilename(), "/Documents/RedBeryl");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error uploading PDF", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OneDriveUploadResponse.error("Upload failed: " + e.getMessage()));
        }
    }

    @PostMapping("/upload-invoice-pdf")
    @Operation(summary = "Upload invoice PDF to OneDrive", description = "Upload an invoice PDF with specific formatting")
    public ResponseEntity<OneDriveUploadResponse> uploadInvoicePdf(
            @RequestHeader("Authorization") String authorization,
            @RequestBody Map<String, Object> request) {
        
        try {
            String accessToken = authorization.replace("Bearer ", "");
            
            if (!oneDriveService.isAuthenticated(accessToken)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(OneDriveUploadResponse.error("Invalid or expired access token"));
            }

            String pdfBase64 = (String) request.get("pdfContent");
            String invoiceNumber = (String) request.get("invoiceNumber");
            
            if (pdfBase64 == null || invoiceNumber == null) {
                return ResponseEntity.badRequest()
                    .body(OneDriveUploadResponse.error("Missing required parameters: pdfContent and invoiceNumber"));
            }

            // Decode base64 PDF content
            byte[] pdfContent = java.util.Base64.getDecoder().decode(pdfBase64);
            
            OneDriveUploadResponse response = oneDriveService.uploadInvoicePdf(accessToken, pdfContent, invoiceNumber);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error uploading invoice PDF", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(OneDriveUploadResponse.error("Upload failed: " + e.getMessage()));
        }
    }

    @GetMapping("/check-auth")
    @Operation(summary = "Check OneDrive authentication", description = "Check if the provided access token is valid")
    public ResponseEntity<Map<String, Object>> checkAuthentication(
            @RequestHeader("Authorization") String authorization) {

        try {
            String accessToken = authorization.replace("Bearer ", "");
            boolean isAuthenticated = oneDriveService.isAuthenticated(accessToken);

            Map<String, Object> response = new HashMap<>();
            response.put("authenticated", isAuthenticated);
            response.put("success", true);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error checking authentication", e);
            Map<String, Object> error = new HashMap<>();
            error.put("authenticated", false);
            error.put("success", false);
            error.put("error", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }

    @GetMapping("/test")
    @Operation(summary = "Test OneDrive API", description = "Simple test endpoint to verify OneDrive API is working")
    public ResponseEntity<Map<String, String>> testOneDriveApi() {
        try {
            Map<String, String> response = new HashMap<>();
            response.put("status", "success");
            response.put("message", "OneDrive API is working!");
            response.put("timestamp", java.time.LocalDateTime.now().toString());
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error in test endpoint", e);
            Map<String, String> error = new HashMap<>();
            error.put("status", "error");
            error.put("message", "OneDrive API test failed: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
        }
    }
}
